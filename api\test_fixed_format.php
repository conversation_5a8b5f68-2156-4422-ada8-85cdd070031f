<?php
/**
 * Тест исправленного формата данных для NOWPayments API
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🔧 ТЕСТ ИСПРАВЛЕННОГО ФОРМАТА ДАННЫХ\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

echo "📊 Проверяем баланс:\n";
$balance = $api->getAccountBalance();
if ($balance) {
    foreach ($balance as $currency => $data) {
        if (is_array($data) && isset($data['amount']) && $data['amount'] > 0) {
            echo "- {$currency}: {$data['amount']}\n";
        }
    }
} else {
    echo "❌ Не удалось получить баланс\n";
}

echo "\n🧪 Тестируем создание выплаты с исправленным форматом:\n";

// Используем USDT TRC20 так как у нас есть баланс
$testAddress = 'TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK'; // Тестовый USDT TRC20 адрес
$testCurrency = 'usdttrc20';
$testAmount = 0.01; // 1 цент

echo "Параметры:\n";
echo "- Адрес: {$testAddress}\n";
echo "- Валюта: {$testCurrency}\n";
echo "- Сумма: {$testAmount}\n\n";

echo "Создание выплаты...\n";

$result = $api->createPayoutWithFeeHandling($testAddress, $testCurrency, $testAmount);

if ($result) {
    if (isset($result['error'])) {
        echo "❌ Ошибка: {$result['message']}\n";
        
        if (isset($result['code'])) {
            echo "Код ошибки: {$result['code']}\n";
        }
        
        if (isset($result['details'])) {
            echo "Детали:\n";
            foreach ($result['details'] as $key => $value) {
                echo "- {$key}: {$value}\n";
            }
        }
    } else {
        echo "✅ ВЫПЛАТА СОЗДАНА УСПЕШНО!\n";
        
        if (isset($result['id'])) {
            echo "🆔 ID выплаты: {$result['id']}\n";
        }
        
        if (isset($result['status'])) {
            echo "📊 Статус: {$result['status']}\n";
        }
        
        if (isset($result['amount'])) {
            echo "💰 Сумма: {$result['amount']} {$result['currency']}\n";
        }
        
        if (isset($result['fee_handling'])) {
            echo "💳 Информация о комиссии:\n";
            echo "- {$result['fee_handling']['note']}\n";
        }
        
        echo "\n📋 Полный ответ API:\n";
        echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
} else {
    echo "❌ Критическая ошибка создания выплаты\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎯 РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ\n";
echo str_repeat("=", 60) . "\n\n";

if ($result && !isset($result['error'])) {
    echo "✅ ПРОБЛЕМА РЕШЕНА!\n";
    echo "Новый формат данных работает корректно.\n";
    echo "Выплаты теперь создаются успешно.\n\n";
    
    echo "🔧 Что было исправлено:\n";
    echo "- Убран массив 'withdrawals'\n";
    echo "- Используется прямой формат данных\n";
    echo "- Сохранена настройка 'fee_paid_by_user: true'\n\n";
    
    echo "🚀 Система готова к работе!\n";
} else {
    echo "⚠️ Требуется дополнительная отладка\n";
    echo "Проверьте логи для получения подробной информации.\n";
}
?>
