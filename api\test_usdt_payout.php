<?php
/**
 * Тест реальной выплаты USDT (у вас есть баланс 1.058 USDT)
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "=== ТЕСТ ВЫПЛАТЫ USDT ===\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Параметры для тестирования
$testCurrency = 'usdttrc20'; // Как показано в балансе
$testAmount = 0.5; // Половина от доступного баланса
$testAddress = 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE'; // TRON USDT адрес

echo "1. Проверка баланса USDT:\n";
$balance = $api->getAccountBalance();
if ($balance && isset($balance['usdttrc20'])) {
    $usdtBalance = $balance['usdttrc20'];
    if (is_array($usdtBalance)) {
        echo "   Доступно: {$usdtBalance['amount']} USDT\n";
        echo "   В ожидании: {$usdtBalance['pendingAmount']} USDT\n";
    } else {
        echo "   Баланс: {$usdtBalance} USDT\n";
    }
} else {
    echo "   ❌ Не удалось получить баланс USDT\n";
    exit;
}

echo "\n2. Проверка минимальной суммы для USDT:\n";
$minAmount = $api->getMinWithdrawalAmount('usdttrc20');
if ($minAmount !== null) {
    echo "   Минимальная сумма: {$minAmount} USDT\n";
    if ($testAmount < $minAmount) {
        $testAmount = $minAmount * 1.1; // Увеличиваем на 10%
        echo "   Увеличиваем тестовую сумму до: {$testAmount} USDT\n";
    }
} else {
    echo "   ⚠️ Не удалось получить минимальную сумму\n";
}

echo "\n3. Оценка комиссии:\n";
$feeEstimate = $api->getWithdrawalFeeEstimate('usdttrc20', $testAmount);
if ($feeEstimate) {
    echo "   Комиссия: " . json_encode($feeEstimate) . "\n";
} else {
    echo "   ⚠️ Не удалось получить оценку комиссии\n";
}

echo "\n4. Параметры тестовой выплаты:\n";
echo "   Валюта: {$testCurrency}\n";
echo "   Сумма: {$testAmount} USDT\n";
echo "   Адрес: {$testAddress}\n";

echo "\n5. Создание выплаты с обработкой комиссий:\n";

// Создаем выплату
$result = $api->createPayoutWithFeeHandling($testAddress, $testCurrency, $testAmount);

if ($result) {
    if (isset($result['error'])) {
        echo "   ❌ Ошибка: {$result['message']}\n";
        echo "   Код: " . ($result['code'] ?? 'UNKNOWN') . "\n";
        
        if (isset($result['details'])) {
            echo "   Детали:\n";
            foreach ($result['details'] as $key => $value) {
                echo "     - {$key}: {$value}\n";
            }
        }
        
        // Показываем полный ответ для отладки
        echo "\n   Полный ответ API:\n";
        echo "   " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
        
    } else {
        echo "   ✅ ВЫПЛАТА СОЗДАНА УСПЕШНО!\n";
        
        if (isset($result['id'])) {
            echo "   ID выплаты: {$result['id']}\n";
        }
        
        if (isset($result['fee_handling'])) {
            echo "   Информация о комиссии:\n";
            echo "     - {$result['fee_handling']['note']}\n";
            
            if (isset($result['fee_handling']['fee_estimate'])) {
                echo "     - Комиссия: " . json_encode($result['fee_handling']['fee_estimate']) . "\n";
            }
        }
        
        echo "\n   Полный ответ API:\n";
        echo "   " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
        
        // Проверяем статус выплаты
        if (isset($result['id'])) {
            echo "\n6. Проверка статуса выплаты:\n";
            sleep(2); // Ждем 2 секунды
            $status = $api->getPayoutStatus($result['id']);
            if ($status) {
                echo "   Статус: " . json_encode($status, JSON_PRETTY_PRINT) . "\n";
            } else {
                echo "   ⚠️ Не удалось получить статус\n";
            }
        }
    }
} else {
    echo "   ❌ Критическая ошибка создания выплаты\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
echo "\nПроверьте панель NOWPayments: https://account.nowpayments.io/payouts\n";
?>
