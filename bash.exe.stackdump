Stack trace:
Frame         Function      Args
0007FFFFA920  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFA920, 0007FFFF9820) msys-2.0.dll+0x2118E
0007FFFFA920  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFA920  0002100469F2 (00021028DF99, 0007FFFFA7D8, 0007FFFFA920, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA920  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA920  00021006A545 (0007FFFFA930, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFA930, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF830570000 ntdll.dll
7FF82EC60000 KERNEL32.DLL
7FF82E070000 KERNELBASE.dll
7FF82B480000 apphelp.dll
7FF82E700000 USER32.dll
7FF82DD00000 win32u.dll
7FF830450000 GDI32.dll
7FF82E4C0000 gdi32full.dll
7FF82DF20000 msvcp_win.dll
7FF82E340000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF82EBA0000 advapi32.dll
7FF82EB00000 msvcrt.dll
7FF830180000 sechost.dll
7FF82E5D0000 RPCRT4.dll
7FF82D520000 CRYPTBASE.DLL
7FF82DD30000 bcryptPrimitives.dll
7FF82FDF0000 IMM32.DLL
