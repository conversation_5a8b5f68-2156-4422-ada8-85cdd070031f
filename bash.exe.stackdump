Stack trace:
Frame         Function      Args
0007FFFFBF10  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBF10, 0007FFFFAE10) msys-2.0.dll+0x2118E
0007FFFFBF10  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFFBF10  0002100469F2 (00021028DF99, 0007FFFFBDC8, 0007FFFFBF10, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBF10  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBF10  00021006A545 (0007FFFFBF20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFBF20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCE3190000 ntdll.dll
7FFCE1360000 KERNEL32.DLL
7FFCE0B70000 KERNELBASE.dll
7FFCDDFB0000 apphelp.dll
7FFCE2FB0000 USER32.dll
7FFCE0970000 win32u.dll
7FFCE1420000 GDI32.dll
7FFCE0FA0000 gdi32full.dll
7FFCE09D0000 msvcp_win.dll
7FFCE0A70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCE1A40000 advapi32.dll
7FFCE1DD0000 msvcrt.dll
7FFCE11F0000 sechost.dll
7FFCE2BC0000 RPCRT4.dll
7FFCE0140000 CRYPTBASE.DLL
7FFCE1160000 bcryptPrimitives.dll
7FFCE2830000 IMM32.DLL
