<!DOCTYPE html>
<html>
<head>
    <title>Тест API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .result { background: #2a2a2a; padding: 15px; margin: 10px 0; border-radius: 5px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #333; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 Тест API getReferralStats</h1>
    
    <button onclick="testDebugAPI()">Тест Debug API</button>
    <button onclick="testReferralStats()">Тест Referral Stats</button>
    <button onclick="clearResults()">Очистить</button>
    
    <div id="results"></div>
    
    <script>
        function addResult(title, data) {
            const results = document.getElementById("results");
            const div = document.createElement("div");
            div.className = "result";
            div.innerHTML = `<h3>${title}</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById("results").innerHTML = "";
        }
        
        async function testDebugAPI() {
            try {
                const response = await fetch("/api/debug_api.php");
                const data = await response.json();
                addResult("🔍 Debug API", data);
            } catch (error) {
                addResult("❌ Debug API Error", { error: error.message });
            }
        }
        
        async function testReferralStats() {
            try {
                // Тестируем с пользователем который должен иметь заработок
                const testData = {
                    initData: "user=" + encodeURIComponent(JSON.stringify({
                        id: 5880288830,
                        username: "alter_mega_ego",
                        first_name: "Альтер",
                        last_name: "Эго"
                    })) + "&auth_date=" + Math.floor(Date.now() / 1000) + "&hash=test_hash"
                };
                
                const response = await fetch("/api/getReferralStats.php", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                addResult("📊 Referral Stats API", data);
            } catch (error) {
                addResult("❌ Referral Stats Error", { error: error.message });
            }
        }
    </script>
</body>
</html>