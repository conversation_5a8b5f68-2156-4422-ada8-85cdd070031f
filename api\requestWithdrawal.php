<?php
/**
 * api/requestWithdrawal.php
 * API эндпоинт для запроса вывода средств через NOWPayments.
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

header('Content-Type: application/json');

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) {
    http_response_code(500);
    error_log('FATAL: config.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: CFG']);
    exit;
}
if (!(@require_once __DIR__ . '/validate_initdata.php')) {
    http_response_code(500);
    error_log('FATAL: validate_initdata.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: VID']);
    exit;
}
if (!(@require_once __DIR__ . '/db_mock.php')) {
    http_response_code(500);
    error_log('FATAL: db_mock.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: DBM']);
    exit;
}
if (!(@require_once __DIR__ . '/security.php')) {
    http_response_code(500);
    error_log('FATAL: security.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: SEC']);
    exit;
}
// --- Конец проверки зависимостей ---

// Подключаем класс для работы с NOWPayments API
if (!(@require_once __DIR__ . '/NOWPaymentsAPI.php')) {
    http_response_code(500);
    error_log('FATAL: NOWPaymentsAPI.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: NPAPI']);
    exit;
}

// 1. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Нет данных']);
    exit;
}

if (!isset($input['amount']) || !is_numeric($input['amount']) || $input['amount'] <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Некорректная сумма']);
    exit;
}

if (!isset($input['crypto_address']) || empty($input['crypto_address'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Не указан адрес кошелька']);
    exit;
}

if (!isset($input['crypto_currency']) || empty($input['crypto_currency'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Не указана криптовалюта']);
    exit;
}

$initData = $input['initData'];
$amount = intval($input['amount']);
$cryptoAddress = trim($input['crypto_address']);
$cryptoCurrency = strtolower(trim($input['crypto_currency']));

// Валидация адреса кошелька в зависимости от выбранной валюты
$isValidAddress = false;
switch ($cryptoCurrency) {
    case 'usdttrc20':
    case 'trx':
        // Адрес TRC20 обычно начинается с T и имеет длину 34 символа
        $isValidAddress = preg_match('/^T[a-zA-Z0-9]{33}$/', $cryptoAddress);
        break;
    case 'btc':
        // Адрес Bitcoin обычно начинается с 1, 3 или bc1 и имеет длину от 26 до 35 символов
        $isValidAddress = preg_match('/^(1|3|bc1)[a-zA-Z0-9]{25,58}$/', $cryptoAddress);
        break;
    case 'eth':
        // Адрес Ethereum начинается с 0x и имеет длину 42 символа
        $isValidAddress = preg_match('/^0x[a-fA-F0-9]{40}$/', $cryptoAddress);
        break;
    case 'bnb':
        // Адрес BNB может начинаться с bnb или 0x
        $isValidAddress = preg_match('/^(bnb1|0x)[a-zA-Z0-9]{38,40}$/', $cryptoAddress);
        break;
    default:
        // Общая проверка - не менее 20 символов
        $isValidAddress = strlen($cryptoAddress) >= 20;
}

if (!$isValidAddress) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Неверный формат адреса кошелька для выбранной криптовалюты']);
    exit;
}

error_log("requestWithdrawal INFO: Получен запрос на вывод {$amount} монет в {$cryptoCurrency} на адрес {$cryptoAddress}");

// 2. Валидация initData
$validatedData = validateTelegramInitData($initData);
if ($validatedData === false) {
    http_response_code(403);
    echo json_encode(['error' => 'Ошибка: Неверные данные']);
    exit;
}
$userId = intval($validatedData['user']['id']);
error_log("requestWithdrawal INFO: initData валидирован для user {$userId}");

// 3. Загрузка данных пользователя
$userData = loadUserData();
if (!is_array($userData)) {
    error_log("requestWithdrawal ERROR: loadUserData вернул не массив");
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера: LD1']);
    exit;
}

// 4. Проверка безопасности и баланса пользователя
if (!isset($userData[$userId]) || !isset($userData[$userId]['balance'])) {
    error_log("requestWithdrawal ERROR: Пользователь {$userId} не найден или нет баланса");
    http_response_code(404);
    echo json_encode(['error' => 'Ошибка: Пользователь не найден']);
    exit;
}

// Проверяем, не заблокирован ли пользователь
if (isset($userData[$userId]['blocked']) && $userData[$userId]['blocked']) {
    error_log("requestWithdrawal WARNING: Попытка вывода средств заблокированным пользователем {$userId}");
    http_response_code(403);
    echo json_encode(['error' => 'Ваш аккаунт заблокирован из-за подозрительной активности']);
    exit;
}

// Проверяем лимит выводов средств
if (!checkWithdrawalLimit($userId, $userData)) {
    error_log("requestWithdrawal WARNING: Превышен лимит выводов средств для пользователя {$userId}");

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, 'withdrawal_limit_exceeded');

    http_response_code(429);
    echo json_encode(['error' => 'Превышен лимит выводов средств. Попробуйте позже']);
    exit;
}

// Проверяем подозрительную активность
if (!checkSuspiciousActivity($userId, $userData)) {
    error_log("requestWithdrawal WARNING: Обнаружена подозрительная активность пользователя {$userId}");
    http_response_code(403);
    echo json_encode(['error' => 'Вывод средств временно ограничен из-за подозрительной активности']);
    exit;
}

$userBalance = $userData[$userId]['balance'];

// Проверяем минимальный баланс для доступа к выводу
if ($userBalance < MIN_BALANCE_FOR_WITHDRAWAL) {
    error_log("requestWithdrawal ERROR: Недостаточный баланс для доступа к выводу у пользователя {$userId}. Баланс: {$userBalance}, Требуется: " . MIN_BALANCE_FOR_WITHDRAWAL);
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка: Для вывода средств необходимо иметь минимум ' . MIN_BALANCE_FOR_WITHDRAWAL . ' монет на балансе']);
    exit;
}

// Проверяем баланс с использованием функции безопасности
if (!verifyBalance($userId, $amount, $userData)) {
    error_log("requestWithdrawal ERROR: Проверка баланса не пройдена для пользователя {$userId}. Запрошено: {$amount}, Доступно: {$userBalance}");

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, 'invalid_withdrawal_amount');

    http_response_code(400);
    echo json_encode(['error' => 'Ошибка: Недостаточно средств или сумма превышает доступную для вывода']);
    exit;
}

// 5. Проверка минимальной суммы вывода (только если установлена)
if (MIN_WITHDRAWAL_AMOUNT > 0 && $amount < MIN_WITHDRAWAL_AMOUNT) {
    error_log("requestWithdrawal ERROR: Сумма вывода меньше минимальной. Запрошено: {$amount}, Минимум: " . MIN_WITHDRAWAL_AMOUNT);
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка: Минимальная сумма для вывода - ' . MIN_WITHDRAWAL_AMOUNT . ' монет']);
    exit;
}

// Логируем событие запроса на вывод средств
logAuditEvent('withdrawal_request', $userId, [
    'amount' => $amount,
    'currency' => $cryptoCurrency,
    'address' => $cryptoAddress
]);

// 6. Конвертация монет в USD с учетом комиссии
$usdAmount = $amount * CONVERSION_RATE;
error_log("requestWithdrawal INFO: Конвертация {$amount} монет в USD: {$usdAmount}");

// 6.1. Получаем данные о валюте и учитываем комиссию
$currencyData = getCurrencyData($cryptoCurrency);
if ($currencyData && isset($currencyData['networkFee'])) {
    $networkFee = $currencyData['networkFee'];
    $usdAmountAfterFee = $usdAmount - $networkFee;

    if ($usdAmountAfterFee <= 0) {
        error_log("requestWithdrawal ERROR: Комиссия ({$networkFee} USD) больше суммы вывода ({$usdAmount} USD)");
        http_response_code(400);
        echo json_encode(['error' => 'Ошибка: Комиссия больше суммы вывода']);
        exit;
    }

    error_log("requestWithdrawal INFO: Учтена комиссия {$networkFee} USD. Сумма после комиссии: {$usdAmountAfterFee} USD");
    $usdAmount = $usdAmountAfterFee;
}

// 7. Создание запроса на вывод через NOWPayments API
try {
    $result = createWithdrawalRequest($userId, $usdAmount, $cryptoCurrency, $cryptoAddress);

    // Проверяем результат создания выплаты
    if ($result === false) {
        error_log("requestWithdrawal ERROR: Не удалось создать запрос на вывод для пользователя {$userId}");
        http_response_code(500);
        echo json_encode(['error' => 'Ошибка: Не удалось создать запрос на вывод']);
        exit;
    }

    // Проверяем на детальные ошибки
    if (isset($result['error'])) {
        error_log("requestWithdrawal ERROR: {$result['error']} для пользователя {$userId}");
        http_response_code(400);
        echo json_encode(['error' => $result['error']]);
        exit;
    }

    // 8. Уменьшение баланса пользователя
    $userData[$userId]['balance'] -= $amount;

    // 9. Сохранение информации о выводе
    if (!isset($userData[$userId]['withdrawals'])) {
        $userData[$userId]['withdrawals'] = [];
    }

    // Добавляем информацию о выводе
    $withdrawalData = [
        'id' => $result['id'] ?? ('manual_' . time()),
        'status' => 'pending',
        'coins_amount' => $amount,
        'usd_amount' => $usdAmount,
        'crypto_amount' => $result['crypto_amount'] ?? null,
        'currency' => $cryptoCurrency,
        'address' => $cryptoAddress,
        'timestamp' => time()
    ];

    $userData[$userId]['withdrawals'][] = $withdrawalData;

    // 10. Сохранение данных пользователя
    if (!saveUserData($userData)) {
        error_log("requestWithdrawal ERROR: Не удалось сохранить данные пользователя {$userId} после вывода");
        http_response_code(500);
        echo json_encode(['error' => 'Ошибка сервера: Не удалось обновить баланс']);
        exit;
    }

    // Логируем успешный вывод
    logAuditEvent('withdrawal_created', $userId, $withdrawalData);

    // 11. Успешный ответ
    $responseMessage = 'Запрос на вывод успешно создан';

    // Добавляем информацию об автоконвертации, если она была использована
    if (isset($result['conversion_info'])) {
        $responseMessage .= '. ' . $result['conversion_info'];
    }

    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => $responseMessage,
        'withdrawal_id' => $result['id'] ?? null,
        'new_balance' => $userData[$userId]['balance'],
        'withdrawal_data' => $withdrawalData,
        'auto_conversion' => $result['auto_conversion'] ?? null,
        'internal_conversion' => $result['internal_conversion'] ?? null
    ]);
    error_log("requestWithdrawal INFO: Успешно создан запрос на вывод для пользователя {$userId}. Новый баланс: {$userData[$userId]['balance']}");

} catch (Exception $e) {
    error_log("requestWithdrawal ERROR: Исключение при создании запроса на вывод: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка: ' . $e->getMessage()]);
    exit;
}

/**
 * Создает запрос на вывод средств через NOWPayments API
 *
 * @param int $userId ID пользователя
 * @param float $amount Сумма в USD
 * @param string $currency Криптовалюта (BTC, ETH, и т.д.)
 * @param string $address Адрес кошелька
 * @return array|false Результат запроса или false в случае ошибки
 */
function createWithdrawalRequest($userId, $amount, $currency, $address) {
    try {
        error_log("createWithdrawalRequest INFO: Начало создания запроса на вывод для пользователя {$userId}");
        error_log("createWithdrawalRequest INFO: Параметры - Amount: {$amount} USD, Currency: {$currency}, Address: {$address}");

        // Создаем экземпляр API клиента
        $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

        // Получаем оценку суммы в выбранной криптовалюте
        $estimate = $api->getEstimateAmount($amount, 'usd', $currency);

        if (!isset($estimate['estimated_amount'])) {
            error_log("createWithdrawalRequest ERROR: Не удалось получить оценку суммы для конвертации {$amount} USD в {$currency}");
            return false;
        }

        $cryptoAmount = $estimate['estimated_amount'];
        error_log("createWithdrawalRequest INFO: Конвертация {$amount} USD в {$cryptoAmount} {$currency}");

        // Сначала пробуем прямую выплату, затем автоконвертацию
        error_log("createWithdrawalRequest INFO: Пробуем прямую выплату {$cryptoAmount} {$currency}");
        $result = $api->createPayoutWithAutoConversion($address, $currency, $cryptoAmount);

        // Проверяем на ошибки
        if (isset($result['error'])) {
            $errorMsg = $result['message'] ?? 'Unknown error';
            $errorCode = $result['code'] ?? 'UNKNOWN';

            // Специальная обработка ошибки недостаточного баланса
            if ($errorCode === 'BAD_CREATE_WITHDRAWAL_REQUEST' && strpos($errorMsg, 'Insufficient balance') !== false) {
                $details = $result['details']['current_values'] ?? [];
                if (isset($details[$currency])) {
                    $requested = $details[$currency]['requested'] ?? 0;
                    $available = $details[$currency]['actualBalance'] ?? 0;
                    $errorMsg = "Недостаточно средств в {$currency}. Запрошено: {$requested}, доступно: {$available}";

                    // Проверяем доступные валюты с балансом
                    $availableCurrency = $api->findCurrencyWithBalance($requested);
                    if ($availableCurrency) {
                        $altCurrency = $availableCurrency['currency'];
                        $altBalance = $availableCurrency['balance'];
                        $errorMsg .= ". Доступна валюта: {$altCurrency} (баланс: {$altBalance})";
                    } else {
                        $errorMsg .= ". Пополните баланс в панели NOWPayments";
                    }
                }
            }

            error_log("createWithdrawalRequest ERROR: {$errorMsg}");
            return ['error' => $errorMsg, 'code' => $errorCode];
        }

        if (!isset($result['id'])) {
            error_log("createWithdrawalRequest ERROR: Не удалось создать выплату. Ответ API: " . json_encode($result));
            return ['error' => 'Неожиданный ответ API'];
        }

        // Проверяем, какой тип конвертации был использован
        if (isset($result['internal_conversion'])) {
            $internalConv = $result['internal_conversion'];
            $targetCurrency = $internalConv['target_currency'];
            $targetAmount = $internalConv['target_amount'];

            error_log("createWithdrawalRequest INFO: Использована внутренняя конвертация NOWPayments: {$targetAmount} {$targetCurrency}");

            // Добавляем информацию о внутренней конвертации в результат
            $result['conversion_info'] = "Выплата создана через внутреннюю конвертацию NOWPayments в {$targetCurrency}";
        } elseif (isset($result['auto_conversion'])) {
            $autoConv = $result['auto_conversion'];
            $originalCurrency = $autoConv['original_request']['currency'];
            $actualCurrency = $autoConv['actual_payout']['currency'];
            $actualAmount = $autoConv['actual_payout']['amount'];

            error_log("createWithdrawalRequest INFO: Использована внешняя автоконвертация: {$actualAmount} {$actualCurrency} -> {$originalCurrency}");

            // Добавляем информацию об автоконвертации в результат
            $result['conversion_info'] = "Выплата создана через автоконвертацию из {$actualCurrency}";
        }

        error_log("createWithdrawalRequest SUCCESS: Создана выплата с ID: {$result['id']}");
        return $result;

    } catch (Exception $e) {
        error_log("createWithdrawalRequest ERROR: Исключение при создании выплаты: " . $e->getMessage());
        return false;
    }
}

/**
 * Получает данные о валюте с комиссиями (синхронизировано с main.js)
 *
 * @param string $currency Код валюты (eth, btc, usdttrc20, trx)
 * @return array|null Данные о валюте или null если не найдена
 */
function getCurrencyData($currency) {
    $currencyData = [
        'eth' => [
            'name' => 'Ethereum (ETH)',
            'minCoins' => 750, // 750 монет = $0.75 (при курсе $0.001 за монету)
            'networkFee' => 0.25, // Комиссия $0.25 (уменьшена для нового курса)
            'status' => 'best'
        ],
        'btc' => [
            'name' => 'Bitcoin (BTC)',
            'minCoins' => 5, // 5 монет = $0.005 (при курсе $0.001 за монету)
            'networkFee' => 0.50, // Комиссия $0.50 (уменьшена для нового курса)
            'status' => 'good'
        ],
        'usdttrc20' => [
            'name' => 'USDT (TRC20)',
            'minCoins' => 8580, // 8580 монет = $8.58 (при курсе $0.001 за монету)
            'networkFee' => 5.58, // Средняя комиссия $5.58 (оставляем как есть)
            'status' => 'expensive'
        ],
        'trx' => [
            'name' => 'TRON (TRX)',
            'minCoins' => 1000, // 1000 монет = $1.0 (при курсе $0.001 за монету)
            'networkFee' => 0.30, // Комиссия $0.30 (уменьшена для нового курса)
            'status' => 'good'
        ]
    ];

    return isset($currencyData[$currency]) ? $currencyData[$currency] : null;
}
?>
