<?php
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    exit(0);
}

require_once "config.php";
require_once "db_mock.php";

$userData = loadUserData();

echo json_encode([
    "debug" => true,
    "users_count" => count($userData),
    "users_with_earnings" => array_filter($userData, function($user) {
        return ($user["referral_earnings"] ?? 0) > 0;
    }),
    "timestamp" => time()
]);
?>