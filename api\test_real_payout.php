<?php
/**
 * Тест реальной выплаты минимальной суммы
 * ВНИМАНИЕ: Этот файл создает РЕАЛЬНЫЕ выплаты!
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

echo "<h1>🧪 Тест реальной выплаты минимальной суммы</h1>\n";
echo "<p><strong>⚠️ ВНИМАНИЕ:</strong> Этот тест создает РЕАЛЬНЫЕ выплаты с реальными деньгами!</p>\n";

// Проверяем баланс аккаунта
echo "<h2>1. Проверка баланса аккаунта</h2>\n";
$balance = $api->getAccountBalance();
if ($balance) {
    echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>\n";
    echo "<strong>✅ Баланс аккаунта:</strong><br>\n";
    foreach ($balance as $currency => $amount) {
        if ($amount > 0) {
            echo "- {$currency}: {$amount}<br>\n";
        }
    }
    echo "</div>\n";
} else {
    echo "<div style='background: #ffe8e8; padding: 10px; border-radius: 5px;'>\n";
    echo "<strong>❌ Не удалось получить баланс аккаунта</strong><br>\n";
    echo "</div>\n";
    exit;
}

// Определяем валюту с наибольшим балансом для теста
$testCurrency = null;
$maxBalance = 0;
foreach ($balance as $currency => $amount) {
    if ($amount > $maxBalance && in_array($currency, ['BTC', 'ETH', 'USDT', 'LTC', 'TRX'])) {
        $maxBalance = $amount;
        $testCurrency = $currency;
    }
}

if (!$testCurrency) {
    echo "<div style='background: #ffe8e8; padding: 10px; border-radius: 5px;'>\n";
    echo "<strong>❌ Нет доступных валют для тестирования</strong><br>\n";
    echo "Пополните баланс одной из валют: BTC, ETH, USDT, LTC, TRX\n";
    echo "</div>\n";
    exit;
}

echo "<h2>2. Выбранная валюта для теста: {$testCurrency}</h2>\n";
echo "<p>Доступный баланс: {$maxBalance} {$testCurrency}</p>\n";

// Получаем минимальную сумму для выплаты
echo "<h2>3. Получение минимальной суммы</h2>\n";
$minAmount = $api->getMinWithdrawalAmount($testCurrency);
if ($minAmount !== null) {
    echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>\n";
    echo "<strong>✅ Минимальная сумма для {$testCurrency}:</strong> {$minAmount}<br>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px;'>\n";
    echo "<strong>⚠️ Не удалось получить минимальную сумму, используем стандартную</strong><br>\n";
    echo "</div>\n";
    
    // Стандартные минимальные суммы
    $standardMins = [
        'BTC' => 0.0001,
        'ETH' => 0.001,
        'USDT' => 1,
        'LTC' => 0.001,
        'TRX' => 10
    ];
    $minAmount = $standardMins[$testCurrency] ?? 0.001;
}

// Проверяем, достаточно ли баланса
if ($maxBalance < $minAmount) {
    echo "<div style='background: #ffe8e8; padding: 10px; border-radius: 5px;'>\n";
    echo "<strong>❌ Недостаточно средств для тестирования</strong><br>\n";
    echo "Нужно: {$minAmount} {$testCurrency}<br>\n";
    echo "Доступно: {$maxBalance} {$testCurrency}<br>\n";
    echo "</div>\n";
    exit;
}

// Тестовый адрес (используем известный адрес для тестирования)
$testAddresses = [
    'BTC' => '**********************************', // Genesis block
    'ETH' => '******************************************', // Null address
    'USDT' => 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE', // TRON USDT
    'LTC' => 'LTC1QW508D6QEJXTDG4Y5R3ZARVARY0C5XW7KV8F3T4', // Bech32
    'TRX' => 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE' // TRON
];

$testAddress = $testAddresses[$testCurrency] ?? $testAddresses['BTC'];

// Получаем оценку комиссии
echo "<h2>4. Оценка комиссии</h2>\n";
$feeEstimate = $api->getWithdrawalFeeEstimate($testCurrency, $minAmount);
if ($feeEstimate) {
    echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>\n";
    echo "<strong>✅ Оценка комиссии:</strong><br>\n";
    echo "<pre>" . json_encode($feeEstimate, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px;'>\n";
    echo "<strong>⚠️ Не удалось получить оценку комиссии</strong><br>\n";
    echo "</div>\n";
}

echo "<h2>5. Параметры тестовой выплаты</h2>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff;'>\n";
echo "<strong>📋 Детали выплаты:</strong><br>\n";
echo "- Валюта: {$testCurrency}<br>\n";
echo "- Сумма: {$minAmount}<br>\n";
echo "- Адрес: {$testAddress}<br>\n";
echo "- Тип: Тестовая выплата с обработкой комиссий<br>\n";
echo "</div>\n";

// Подтверждение от пользователя
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<h2>⚠️ Подтверждение</h2>\n";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>\n";
    echo "<p><strong>Вы уверены, что хотите создать РЕАЛЬНУЮ выплату?</strong></p>\n";
    echo "<p>Это будет стоить реальные деньги из вашего баланса NOWPayments!</p>\n";
    echo "<a href='?confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🚀 ДА, СОЗДАТЬ ВЫПЛАТУ</a>\n";
    echo "<a href='test_fee_handling.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>❌ НЕТ, ТОЛЬКО ТЕСТИРОВАНИЕ</a>\n";
    echo "</div>\n";
    exit;
}

echo "<h2>6. 🚀 Создание реальной выплаты</h2>\n";
echo "<p>Создаем выплату с новой системой обработки комиссий...</p>\n";

// Создаем реальную выплату
$result = $api->createPayoutWithFeeHandling($testAddress, $testCurrency, $minAmount);

if ($result) {
    if (isset($result['error'])) {
        echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 5px;'>\n";
        echo "<strong>❌ Ошибка при создании выплаты:</strong><br>\n";
        echo "<strong>Код:</strong> " . ($result['code'] ?? 'UNKNOWN') . "<br>\n";
        echo "<strong>Сообщение:</strong> " . $result['message'] . "<br>\n";
        
        if (isset($result['details'])) {
            echo "<strong>Детали:</strong><br>\n";
            foreach ($result['details'] as $key => $value) {
                echo "- {$key}: {$value}<br>\n";
            }
        }
        echo "</div>\n";
    } else {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>\n";
        echo "<strong>✅ ВЫПЛАТА СОЗДАНА УСПЕШНО!</strong><br><br>\n";
        
        // Показываем ID выплаты
        if (isset($result['id'])) {
            echo "<strong>🆔 ID выплаты:</strong> {$result['id']}<br>\n";
        }
        
        // Показываем информацию о комиссиях
        if (isset($result['fee_handling'])) {
            echo "<strong>💳 Информация о комиссии:</strong><br>\n";
            echo "- {$result['fee_handling']['note']}<br>\n";
            
            if (isset($result['fee_handling']['fee_estimate'])) {
                $fee = $result['fee_handling']['fee_estimate'];
                echo "- Комиссия: " . json_encode($fee) . "<br>\n";
            }
            
            if (isset($result['fee_handling']['min_amount'])) {
                echo "- Минимальная сумма: {$result['fee_handling']['min_amount']} {$testCurrency}<br>\n";
            }
        }
        
        echo "<br><strong>📄 Полный ответ API:</strong><br>\n";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
        echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        echo "</pre>\n";
        echo "</div>\n";
        
        // Ссылка для проверки статуса
        if (isset($result['id'])) {
            echo "<h2>7. 🔍 Проверка статуса</h2>\n";
            echo "<p><a href='check_payout_status.php?id={$result['id']}' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Проверить статус выплаты</a></p>\n";
        }
    }
} else {
    echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 5px;'>\n";
    echo "<strong>❌ Критическая ошибка:</strong> Не удалось создать выплату<br>\n";
    echo "Проверьте логи ошибок для получения дополнительной информации.\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<h2>📊 Результат тестирования</h2>\n";
echo "<p>Тест завершен. Проверьте панель NOWPayments для подтверждения создания выплаты:</p>\n";
echo "<p><a href='https://account.nowpayments.io/payouts' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 Открыть панель NOWPayments</a></p>\n";
?>
