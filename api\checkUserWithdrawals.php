<?php
/**
 * api/checkUserWithdrawals.php
 * Проверка и обновление статусов выплат для конкретного пользователя
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db_mock.php';
require_once __DIR__ . '/security.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

try {
    // 1. Получение и валидация входных данных
    $inputJSON = file_get_contents('php://input');
    $input = json_decode($inputJSON, true);

    if (!isset($input['initData']) || empty($input['initData'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Отсутствуют данные аутентификации']);
        exit;
    }

    // 2. Валидация initData
    $validatedData = validateInitData($input['initData']);
    if (!$validatedData) {
        error_log("checkUserWithdrawals ERROR: Невалидный initData");
        http_response_code(401);
        echo json_encode(['error' => 'Ошибка аутентификации']);
        exit;
    }

    $userId = intval($validatedData['user']['id']);
    error_log("checkUserWithdrawals INFO: Проверка выплат для пользователя {$userId}");

    // 3. Загрузка данных пользователя
    $userData = loadUserData();
    if (!is_array($userData) || !isset($userData[$userId])) {
        http_response_code(404);
        echo json_encode(['error' => 'Пользователь не найден']);
        exit;
    }

    $user = $userData[$userId];
    $withdrawals = $user['withdrawals'] ?? [];

    if (empty($withdrawals)) {
        echo json_encode([
            'success' => true,
            'withdrawals' => [],
            'updated_count' => 0,
            'message' => 'Нет выплат для проверки'
        ]);
        exit;
    }

    // 4. Создаем экземпляр API
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

    $updatedCount = 0;
    $statusChanges = [];

    // 5. Проверяем статус каждой выплаты
    foreach ($withdrawals as $index => &$withdrawal) {
        $payoutId = $withdrawal['payout_id'] ?? null;
        $currentStatus = $withdrawal['status'] ?? 'unknown';

        // Пропускаем уже завершенные выплаты
        if (in_array($currentStatus, ['completed', 'finished', 'failed', 'cancelled', 'expired'])) {
            continue;
        }

        if (!$payoutId) {
            error_log("checkUserWithdrawals WARNING: Выплата без payout_id: " . json_encode($withdrawal));
            continue;
        }

        try {
            // Проверяем статус через NOWPayments API
            $statusResponse = $api->getPayoutStatus($payoutId);
            
            if ($statusResponse && isset($statusResponse['status'])) {
                $newStatus = $statusResponse['status'];
                
                // Если статус изменился
                if ($newStatus !== $currentStatus) {
                    $oldStatus = $currentStatus;
                    $withdrawal['status'] = $newStatus;
                    $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                    
                    // Добавляем дополнительную информацию из API
                    if (isset($statusResponse['amount'])) {
                        $withdrawal['actual_amount'] = $statusResponse['amount'];
                    }
                    if (isset($statusResponse['fee'])) {
                        $withdrawal['actual_fee'] = $statusResponse['fee'];
                    }
                    if (isset($statusResponse['hash'])) {
                        $withdrawal['transaction_hash'] = $statusResponse['hash'];
                    }

                    $updatedCount++;
                    $statusChanges[] = [
                        'withdrawal_id' => $withdrawal['id'] ?? $index,
                        'old_status' => $oldStatus,
                        'new_status' => $newStatus,
                        'amount' => $withdrawal['coins_amount'] ?? 0,
                        'currency' => $withdrawal['currency'] ?? 'unknown'
                    ];

                    error_log("checkUserWithdrawals INFO: Статус выплаты {$payoutId} изменен: {$oldStatus} -> {$newStatus}");
                }
            }
        } catch (Exception $e) {
            error_log("checkUserWithdrawals WARNING: Ошибка проверки статуса {$payoutId}: " . $e->getMessage());
            continue;
        }
    }

    // 6. Сохраняем обновленные данные
    if ($updatedCount > 0) {
        $userData[$userId]['withdrawals'] = $withdrawals;
        saveUserData($userData);
        error_log("checkUserWithdrawals INFO: Обновлено {$updatedCount} выплат для пользователя {$userId}");
    }

    // 7. Сортируем выплаты по времени (новые сначала)
    usort($withdrawals, function($a, $b) {
        return strtotime($b['created_at'] ?? '1970-01-01') - strtotime($a['created_at'] ?? '1970-01-01');
    });

    // 8. Возвращаем результат
    echo json_encode([
        'success' => true,
        'withdrawals' => $withdrawals,
        'updated_count' => $updatedCount,
        'status_changes' => $statusChanges,
        'total_withdrawals' => count($withdrawals),
        'checked_at' => date('Y-m-d H:i:s'),
        'message' => $updatedCount > 0 ? 
            "Обновлено статусов: {$updatedCount}" : 
            "Все статусы актуальны"
    ]);

} catch (Exception $e) {
    error_log("checkUserWithdrawals CRITICAL ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка сервера при проверке статусов',
        'details' => $e->getMessage()
    ]);
}
?>
