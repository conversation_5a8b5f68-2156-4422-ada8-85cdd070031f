<?php
/**
 * Простой тест вывода средств
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🧪 ПРОСТОЙ ТЕСТ ВЫВОДА СРЕДСТВ\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

echo "📊 Проверяем баланс:\n";
$balance = $api->getAccountBalance();
if ($balance) {
    foreach ($balance as $currency => $data) {
        if (is_array($data) && isset($data['amount']) && $data['amount'] > 0) {
            echo "- {$currency}: {$data['amount']}\n";
        }
    }
} else {
    echo "❌ Не удалось получить баланс\n";
    exit;
}

echo "\n🎯 Тестируем создание выплаты:\n";

// Тестовые параметры
$testAddress = 'TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK';
$testCurrency = 'usdttrc20';
$testAmount = 0.02; // 2 цента

echo "Параметры:\n";
echo "- Адрес: {$testAddress}\n";
echo "- Валюта: {$testCurrency}\n";
echo "- Сумма: {$testAmount}\n\n";

// Создаем выплату
$result = $api->createPayoutWithFeeHandling($testAddress, $testCurrency, $testAmount);

if ($result) {
    if (isset($result['error'])) {
        echo "❌ Ошибка: {$result['message']}\n";
        if (isset($result['code'])) {
            echo "Код: {$result['code']}\n";
        }
    } else {
        echo "✅ ВЫПЛАТА СОЗДАНА!\n";
        echo "🆔 ID: {$result['id']}\n";
        
        if (isset($result['withdrawals'][0])) {
            $withdrawal = $result['withdrawals'][0];
            echo "📊 Статус: {$withdrawal['status']}\n";
            echo "💰 Сумма: {$withdrawal['amount']} {$withdrawal['currency']}\n";
            echo "📍 Адрес: {$withdrawal['address']}\n";
        }
    }
} else {
    echo "❌ Критическая ошибка\n";
}

echo "\n" . str_repeat("=", 50) . "\n";

if ($result && !isset($result['error'])) {
    echo "🎉 ИСПРАВЛЕНИЕ РАБОТАЕТ!\n";
    echo "Проблема с форматом данных решена.\n";
    echo "Выплаты создаются успешно.\n\n";
    
    echo "🔧 Что было исправлено:\n";
    echo "- Убраны проблемные поля из запроса\n";
    echo "- Оставлены только обязательные поля\n";
    echo "- Сохранен массив 'withdrawals'\n\n";
    
    echo "✅ Теперь можно тестировать через веб-интерфейс!\n";
    echo "Откройте http://argun-defolt.loc/ и попробуйте вывести средства.\n";
} else {
    echo "⚠️ Требуется дополнительная отладка\n";
}
?>
