<!DOCTYPE html>
<html>
<head>
    <title>Тест элементов интерфейса</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .result { background: #2a2a2a; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #333; padding: 10px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; }
        .highlight { background: #ffc107; color: #000; padding: 2px 4px; border-radius: 3px; }
        iframe { width: 100%; height: 600px; border: 1px solid #444; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 Тест элементов интерфейса</h1>
    
    <div style="margin-bottom: 20px;">
        <button onclick="testMainApp()">📱 Тест основного приложения</button>
        <button onclick="testAPIDirectly()">📡 Тест API напрямую</button>
        <button onclick="simulateReferralStats()">🎯 Симуляция статистики</button>
        <button onclick="clearResults()">🗑️ Очистить</button>
    </div>
    
    <div id="results"></div>
    
    <div style="margin-top: 20px;">
        <h3>📱 Основное приложение:</h3>
        <iframe id="main-app" src="/"></iframe>
    </div>
    
    <script>
        function addResult(title, data, isError = false) {
            const results = document.getElementById("results");
            const div = document.createElement("div");
            div.className = "result " + (isError ? "error" : "success");
            
            let content = `<h3>${title}</h3>`;
            
            if (typeof data === "object") {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                // Выделяем важные поля
                if (data.totalEarned !== undefined) {
                    content += `<p><span class="highlight">totalEarned: ${data.totalEarned}</span></p>`;
                }
            } else {
                content += `<pre>${data}</pre>`;
            }
            
            div.innerHTML = content;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById("results").innerHTML = "";
        }
        
        async function testMainApp() {
            try {
                // Проверяем доступность основного приложения
                const response = await fetch("/");
                const html = await response.text();
                
                // Проверяем наличие ключевых элементов
                const hasReferralEarnings = html.includes('referral-earnings');
                const hasReferralStats = html.includes('referral-stats');
                const hasMainJS = html.includes('main.js');
                
                addResult("📱 Основное приложение", {
                    status: response.status,
                    hasReferralEarnings: hasReferralEarnings,
                    hasReferralStats: hasReferralStats,
                    hasMainJS: hasMainJS,
                    htmlLength: html.length
                });
                
                // Обновляем iframe
                const iframe = document.getElementById('main-app');
                iframe.src = iframe.src; // Перезагружаем iframe
                
            } catch (error) {
                addResult("❌ Ошибка основного приложения", { error: error.message }, true);
            }
        }
        
        async function testAPIDirectly() {
            try {
                // Тестируем простой API
                const simpleResponse = await fetch("/api/simple_test.php");
                const simpleData = await simpleResponse.json();
                addResult("🧪 Простой API", simpleData);
                
                // Тестируем реальный API
                const testData = {
                    initData: "user=" + encodeURIComponent(JSON.stringify({
                        id: 5880288830,
                        username: "alter_mega_ego",
                        first_name: "Альтер",
                        last_name: "Эго"
                    })) + "&auth_date=" + Math.floor(Date.now() / 1000) + "&hash=test_hash"
                };
                
                const realResponse = await fetch("/api/getReferralStats.php", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(testData)
                });
                
                const realData = await realResponse.json();
                addResult("📊 Реальный API getReferralStats", realData);
                
            } catch (error) {
                addResult("❌ Ошибка API", { error: error.message }, true);
            }
        }
        
        async function simulateReferralStats() {
            try {
                // Симулируем то что должно происходить в приложении
                const testData = {
                    initData: "user=" + encodeURIComponent(JSON.stringify({
                        id: 5880288830,
                        username: "alter_mega_ego",
                        first_name: "Альтер",
                        last_name: "Эго"
                    })) + "&auth_date=" + Math.floor(Date.now() / 1000) + "&hash=test_hash"
                };
                
                const response = await fetch("/api/getReferralStats.php", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                // Симулируем обновление интерфейса
                const simulatedUpdate = {
                    receivedData: data,
                    referralsCountText: data.referralsCount || 0,
                    referralEarningsText: (data.totalEarned || 0) + " монет",
                    expectedEarnings: 12, // Ожидаемый заработок из данных
                    isCorrect: (data.totalEarned || 0) === 12
                };
                
                addResult("🎯 Симуляция обновления интерфейса", simulatedUpdate);
                
                // Проверяем что должно отображаться
                if (simulatedUpdate.isCorrect) {
                    addResult("✅ Данные корректны", {
                        message: "API возвращает правильный заработок",
                        shouldDisplay: simulatedUpdate.referralEarningsText
                    });
                } else {
                    addResult("❌ Данные некорректны", {
                        message: "API возвращает неправильный заработок",
                        expected: "12 монет",
                        received: simulatedUpdate.referralEarningsText
                    }, true);
                }
                
            } catch (error) {
                addResult("❌ Ошибка симуляции", { error: error.message }, true);
            }
        }
        
        // Автоматически запускаем тесты при загрузке
        window.addEventListener('load', function() {
            setTimeout(() => {
                testAPIDirectly();
            }, 1000);
        });
    </script>
</body>
</html>
