[04-Jun-2025 16:41:54 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:54 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 63)
[04-Jun-2025 16:41:55 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:55 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:55 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:55 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:55 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:55 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:56 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:56 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:56 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:56 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:56 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:56 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:57 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:57 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:57 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:57 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:57 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:57 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:50:31 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:31 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 2076
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 1632
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:52 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:52 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:52 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:53 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:53 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:55 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:55 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:56 Europe/Moscow] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[04-Jun-2025 16:50:56 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[04-Jun-2025 16:50:56 Europe/Moscow] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[04-Jun-2025 16:50:57 Europe/Moscow] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A"}
[04-Jun-2025 16:50:57 Europe/Moscow] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[04-Jun-2025 16:50:57 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:50:58 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:50:58 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:50:58 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:59 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.0001
[04-Jun-2025 16:50:59 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:00 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:01 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:01 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:01 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:02 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[04-Jun-2025 16:51:02 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:04 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:05 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:05 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:06 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[04-Jun-2025 16:51:06 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:07 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:08 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:08 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:08 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:09 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[04-Jun-2025 16:51:09 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:09 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:51:19 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:19 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 1021
[04-Jun-2025 16:51:30 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:30 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:51:35 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:35 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:51:35 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:36 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:37 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:37 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:38 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:39 Europe/Moscow] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[04-Jun-2025 16:51:39 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[04-Jun-2025 16:51:39 Europe/Moscow] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[04-Jun-2025 16:51:40 Europe/Moscow] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE"}
[04-Jun-2025 16:51:40 Europe/Moscow] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[04-Jun-2025 16:51:40 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:40 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:41 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:41 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:42 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.0001
[04-Jun-2025 16:51:42 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:42 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:43 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:44 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:44 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:45 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[04-Jun-2025 16:51:45 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:46 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:46 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:46 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:47 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[04-Jun-2025 16:51:47 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:48 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:49 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:49 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:49 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:50 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[04-Jun-2025 16:51:50 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:50 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:52:21 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:52:21 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:52:24 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:52:24 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
