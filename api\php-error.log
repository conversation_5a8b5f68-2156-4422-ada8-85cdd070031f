[04-Jun-2025 16:41:54 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:54 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 63)
[04-Jun-2025 16:41:55 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:55 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:55 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:55 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:55 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:55 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:55 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:56 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:56 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:56 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:56 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:56 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:56 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:56 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:57 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:57 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:57 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 65)
[04-Jun-2025 16:41:57 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 16:41:57 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:41:57 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 16:41:57 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 0
[04-Jun-2025 16:50:31 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:31 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 2076
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:50:32 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 1632
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:32 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:52 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:50:52 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:50:52 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:53 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:53 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:55 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:55 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:56 Europe/Moscow] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[04-Jun-2025 16:50:56 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[04-Jun-2025 16:50:56 Europe/Moscow] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[04-Jun-2025 16:50:57 Europe/Moscow] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A"}
[04-Jun-2025 16:50:57 Europe/Moscow] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[04-Jun-2025 16:50:57 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:50:58 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:50:58 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:50:58 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:50:59 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.0001
[04-Jun-2025 16:50:59 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:00 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:01 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:01 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:01 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:02 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[04-Jun-2025 16:51:02 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:04 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:05 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:05 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:06 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[04-Jun-2025 16:51:06 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:07 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:08 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwNTQsImV4cCI6MTc0OTA0NTM1NH0.6fnr4fOYim4RPmBibvNr_myyVXhs46HJFUt85ckYI-A","Content-Type: application\/json"]
[04-Jun-2025 16:51:08 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:08 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:09 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[04-Jun-2025 16:51:09 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:09 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:51:19 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:19 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:51:19 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 1021
[04-Jun-2025 16:51:30 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:30 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:51:35 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:35 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:51:35 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:36 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:37 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:37 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:38 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:39 Europe/Moscow] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[04-Jun-2025 16:51:39 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[04-Jun-2025 16:51:39 Europe/Moscow] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[04-Jun-2025 16:51:40 Europe/Moscow] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE"}
[04-Jun-2025 16:51:40 Europe/Moscow] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[04-Jun-2025 16:51:40 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:40 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:41 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:41 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:42 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.0001
[04-Jun-2025 16:51:42 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:42 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:43 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:44 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:44 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:45 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[04-Jun-2025 16:51:45 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:46 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:46 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:46 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:47 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[04-Jun-2025 16:51:47 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:48 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:49 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNDUwOTcsImV4cCI6MTc0OTA0NTM5N30.mq_dbKRHFLCJHdT2cAn9sb8LJ7sJIDVD0HYA4r2dvtE","Content-Type: application\/json"]
[04-Jun-2025 16:51:49 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 16:51:49 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 16:51:50 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[04-Jun-2025 16:51:50 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:51:50 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:52:21 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:52:21 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:52:24 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:52:24 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:36:48 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:36:48 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:36:48 Europe/Moscow] registerReferral INFO: Получен initData (длина: 297) и referrerId: 12345
[04-Jun-2025 18:36:49 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: Упрощенная валидация прошла для пользователя 999888777
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: initData успешно валидирован для пользователя 999888777
[04-Jun-2025 18:36:49 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:36:49 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: Данные пользователей загружены для user 999888777 и referrer 12345.
[04-Jun-2025 18:36:49 Europe/Moscow] db_mock INFO: User 999888777 created with referrer 12345 Current userData count: 4
[04-Jun-2025 18:36:49 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 999888777
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: Создан новый пользователь 999888777 с реферером 12345
[04-Jun-2025 18:36:49 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 18:36:49 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 18:36:49 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 18:36:49 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 18:36:49 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 2250
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: Данные пользователя 999888777 и реферера 12345 сохранены.
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: Успешный ответ отправлен для user 999888777 и referrer 12345.
[04-Jun-2025 15:36:49 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:36:49 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:36:49 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_referral_system_complete.php on line 136
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 63)
[04-Jun-2025 18:36:49 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 18:36:49 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:36:49 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 18:36:49 Europe/Moscow] PHP Fatal error:  Uncaught TypeError: Illegal offset type in isset or empty in D:\OSPanel\domains\argun-defolt.loc\api\getReferralStats.php:97
Stack trace:
#0 {main}
  thrown in D:\OSPanel\domains\argun-defolt.loc\api\getReferralStats.php on line 97
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 67)
[04-Jun-2025 18:36:49 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 999888777
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 999888777
[04-Jun-2025 18:36:49 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:36:49 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 999888777.
[04-Jun-2025 18:36:49 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 999888777. Рефералов: 0
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: Получен initData (длина: 297) и referrerId: 12345
[04-Jun-2025 18:36:49 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: Упрощенная валидация прошла для пользователя 999888777
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: initData успешно валидирован для пользователя 999888777
[04-Jun-2025 18:36:49 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:36:49 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: Данные пользователей загружены для user 999888777 и referrer 12345.
[04-Jun-2025 18:36:49 Europe/Moscow] registerReferral INFO: Пользователь 999888777 уже имеет реферера 12345
[04-Jun-2025 15:36:49 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:36:49 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:36:49 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 15:36:49 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 15:36:49 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 15:36:49 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 15:36:49 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 1772
[04-Jun-2025 15:36:49 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:36:49 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:36:49 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:36:49 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Получен initData (длина: 297) и referrerId: 12345
[04-Jun-2025 18:39:36 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Упрощенная валидация прошла для пользователя 999888777
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: initData успешно валидирован для пользователя 999888777
[04-Jun-2025 18:39:36 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:39:36 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Данные пользователей загружены для user 999888777 и referrer 12345.
[04-Jun-2025 18:39:36 Europe/Moscow] db_mock INFO: User 999888777 created with referrer 12345 Current userData count: 4
[04-Jun-2025 18:39:36 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 999888777
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Создан новый пользователь 999888777 с реферером 12345
[04-Jun-2025 18:39:36 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 18:39:36 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 18:39:36 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 18:39:36 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 18:39:36 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 2250
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Данные пользователя 999888777 и реферера 12345 сохранены.
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Успешный ответ отправлен для user 999888777 и referrer 12345.
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:39:36 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_referral_system_complete.php on line 136
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 63)
[04-Jun-2025 18:39:36 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 18:39:36 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:39:36 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats WARNING: Некорректный ID реферала: array (
  'user_id' => 5880288830,
  'first_name' => 'Альтер',
  'last_name' => 'Эго',
  'username' => 'alter_mega_ego',
  'registered_at' => 1749045378,
)
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 2
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 67)
[04-Jun-2025 18:39:36 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 999888777
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 999888777
[04-Jun-2025 18:39:36 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:39:36 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 999888777.
[04-Jun-2025 18:39:36 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 999888777. Рефералов: 0
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Получен initData (длина: 297) и referrerId: 12345
[04-Jun-2025 18:39:36 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Упрощенная валидация прошла для пользователя 999888777
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: initData успешно валидирован для пользователя 999888777
[04-Jun-2025 18:39:36 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:39:36 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Данные пользователей загружены для user 999888777 и referrer 12345.
[04-Jun-2025 18:39:36 Europe/Moscow] registerReferral INFO: Пользователь 999888777 уже имеет реферера 12345
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:39:36 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 15:39:36 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 15:39:36 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 15:39:36 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 15:39:36 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 1772
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:39:36 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:39:51 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:39:51 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:41:19 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:41:19 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:41:19 Europe/Moscow] registerReferral INFO: Получен initData (длина: 267) и referrerId: 12345
[04-Jun-2025 18:41:19 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:41:19 Europe/Moscow] registerReferral WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:41:19 Europe/Moscow] registerReferral INFO: Упрощенная валидация прошла для пользователя 888777666
[04-Jun-2025 18:41:19 Europe/Moscow] registerReferral INFO: initData успешно валидирован для пользователя 888777666
[04-Jun-2025 18:41:19 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:41:19 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:41:19 Europe/Moscow] registerReferral INFO: Данные пользователей загружены для user 888777666 и referrer 12345.
[04-Jun-2025 18:41:19 Europe/Moscow] db_mock INFO: User 888777666 created with referrer 12345 Current userData count: 4
[04-Jun-2025 18:41:19 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 888777666
[04-Jun-2025 18:41:19 Europe/Moscow] registerReferral INFO: Создан новый пользователь 888777666 с реферером 12345
[04-Jun-2025 18:41:19 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 18:41:19 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 18:41:19 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 18:41:19 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 18:41:19 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 2244
[04-Jun-2025 18:41:19 Europe/Moscow] registerReferral INFO: Данные пользователя 888777666 и реферера 12345 сохранены.
[04-Jun-2025 18:41:19 Europe/Moscow] registerReferral INFO: Успешный ответ отправлен для user 888777666 и referrer 12345.
[04-Jun-2025 18:41:19 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 267)
[04-Jun-2025 18:41:19 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:41:19 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:41:19 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 888777666
[04-Jun-2025 18:41:19 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 888777666
[04-Jun-2025 18:41:19 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:41:19 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:41:19 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 888777666.
[04-Jun-2025 18:41:19 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 888777666. Рефералов: 0
[04-Jun-2025 15:41:19 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:41:19 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:41:19 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 15:41:19 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 15:41:19 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 15:41:19 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 15:41:19 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 1772
[04-Jun-2025 15:42:28 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:42:28 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:42:28 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 15:42:28 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 15:42:28 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 15:42:28 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 15:42:28 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 2004
[04-Jun-2025 15:42:43 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:42:43 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:42:43 Europe/Moscow] registerReferral INFO: Получен initData (длина: 267) и referrerId: 12345
[04-Jun-2025 18:42:43 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:42:43 Europe/Moscow] registerReferral WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:42:43 Europe/Moscow] registerReferral INFO: Упрощенная валидация прошла для пользователя 888777666
[04-Jun-2025 18:42:43 Europe/Moscow] registerReferral INFO: initData успешно валидирован для пользователя 888777666
[04-Jun-2025 18:42:43 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:42:43 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:42:43 Europe/Moscow] registerReferral INFO: Данные пользователей загружены для user 888777666 и referrer 12345.
[04-Jun-2025 18:42:43 Europe/Moscow] db_mock INFO: User 888777666 created with referrer 12345 Current userData count: 4
[04-Jun-2025 18:42:43 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 888777666
[04-Jun-2025 18:42:43 Europe/Moscow] registerReferral INFO: Создан новый пользователь 888777666 с реферером 12345
[04-Jun-2025 18:42:43 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 18:42:43 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 18:42:43 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 18:42:43 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 18:42:43 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 2476
[04-Jun-2025 18:42:43 Europe/Moscow] registerReferral INFO: Данные пользователя 888777666 и реферера 12345 сохранены.
[04-Jun-2025 18:42:43 Europe/Moscow] registerReferral INFO: Успешный ответ отправлен для user 888777666 и referrer 12345.
[04-Jun-2025 18:42:43 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 267)
[04-Jun-2025 18:42:43 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 18:42:43 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 18:42:43 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 888777666
[04-Jun-2025 18:42:43 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 888777666
[04-Jun-2025 18:42:43 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 18:42:43 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 18:42:43 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 888777666.
[04-Jun-2025 18:42:43 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 888777666. Рефералов: 0
[04-Jun-2025 15:42:43 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 15:42:43 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 15:42:43 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 15:42:43 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 15:42:43 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 15:42:43 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 15:42:43 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 2004
[04-Jun-2025 16:07:48 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:07:48 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Получен initData (длина: 311)
[04-Jun-2025 19:07:48 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Упрощенная валидация прошла для пользователя 777666555
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: initData успешно валидирован для пользователя 777666555
[04-Jun-2025 19:07:48 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:07:48 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Данные пользователей загружены для user 777666555.
[04-Jun-2025 19:07:48 Europe/Moscow] db_mock INFO: User 777666555 created. Current userData count: 4
[04-Jun-2025 19:07:48 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 777666555
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Детали пользователя 777666555 получены. Баланс: 0
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 2460
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Данные пользователя 777666555 сохранены (на случай создания).
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Успешный ответ отправлен для user 777666555.
[04-Jun-2025 19:07:48 Europe/Moscow] registerReferral INFO: Получен initData (длина: 345) и referrerId: 777666555
[04-Jun-2025 19:07:48 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:07:48 Europe/Moscow] registerReferral WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:07:48 Europe/Moscow] registerReferral INFO: Упрощенная валидация прошла для пользователя 666555444
[04-Jun-2025 19:07:48 Europe/Moscow] registerReferral INFO: initData успешно валидирован для пользователя 666555444
[04-Jun-2025 19:07:48 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:07:48 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:07:48 Europe/Moscow] registerReferral INFO: Данные пользователей загружены для user 666555444 и referrer 777666555.
[04-Jun-2025 19:07:48 Europe/Moscow] db_mock INFO: User 666555444 created with referrer 777666555 Current userData count: 5
[04-Jun-2025 19:07:48 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 666555444
[04-Jun-2025 19:07:48 Europe/Moscow] registerReferral INFO: Создан новый пользователь 666555444 с реферером 777666555
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 2962
[04-Jun-2025 19:07:48 Europe/Moscow] registerReferral INFO: Данные пользователя 666555444 и реферера 777666555 сохранены.
[04-Jun-2025 19:07:48 Europe/Moscow] registerReferral INFO: Успешный ответ отправлен для user 666555444 и referrer 777666555.
[04-Jun-2025 16:07:48 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:07:48 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Получен initData (длина: 311)
[04-Jun-2025 19:07:48 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Упрощенная валидация прошла для пользователя 777666555
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: initData успешно валидирован для пользователя 777666555
[04-Jun-2025 19:07:48 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:07:48 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Данные пользователей загружены для user 777666555.
[04-Jun-2025 19:07:48 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 777666555
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Детали пользователя 777666555 получены. Баланс: 0
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 19:07:48 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 2962
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Данные пользователя 777666555 сохранены (на случай создания).
[04-Jun-2025 19:07:48 Europe/Moscow] getUserData INFO: Успешный ответ отправлен для user 777666555.
[04-Jun-2025 19:07:48 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 345)
[04-Jun-2025 19:07:48 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:07:48 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:07:48 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 666555444
[04-Jun-2025 19:07:48 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 666555444
[04-Jun-2025 19:07:48 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:07:48 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:07:48 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 666555444.
[04-Jun-2025 19:07:48 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 666555444. Рефералов: 0
[04-Jun-2025 16:07:48 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:07:48 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:07:48 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:07:48 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:07:48 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:07:48 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:07:48 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 2004
[04-Jun-2025 16:07:48 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:07:48 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:13:16 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:13:16 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:13:16 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:13:16 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:13:16 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:13:16 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:13:16 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 3768
[04-Jun-2025 19:13:17 Europe/Moscow] getUserData INFO: Получен initData (длина: 286)
[04-Jun-2025 19:13:17 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:13:17 Europe/Moscow] getUserData WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:13:17 Europe/Moscow] getUserData INFO: Упрощенная валидация прошла для пользователя 888999000
[04-Jun-2025 19:13:17 Europe/Moscow] getUserData INFO: initData успешно валидирован для пользователя 888999000
[04-Jun-2025 19:13:17 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:13:17 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:13:17 Europe/Moscow] getUserData INFO: Данные пользователей загружены для user 888999000.
[04-Jun-2025 19:13:17 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 888999000
[04-Jun-2025 19:13:17 Europe/Moscow] getUserData INFO: Детали пользователя 888999000 получены. Баланс: 150
[04-Jun-2025 19:13:17 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 19:13:17 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 19:13:17 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 19:13:17 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 19:13:17 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 3795
[04-Jun-2025 19:13:17 Europe/Moscow] getUserData INFO: Данные пользователя 888999000 сохранены (на случай создания).
[04-Jun-2025 19:13:17 Europe/Moscow] getUserData INFO: Успешный ответ отправлен для user 888999000.
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 286)
[04-Jun-2025 19:13:17 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 888999000
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 888999000
[04-Jun-2025 19:13:17 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:13:17 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 888999000.
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 888999000. Рефералов: 3
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 223)
[04-Jun-2025 19:13:17 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 888999001
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 888999001
[04-Jun-2025 19:13:17 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:13:17 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 888999001.
[04-Jun-2025 19:13:17 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 888999001. Рефералов: 0
[04-Jun-2025 16:13:17 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:13:17 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:13:17 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:13:17 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:13:17 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:13:17 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:13:17 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 2004
[04-Jun-2025 19:13:47 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:13:51 Europe/Moscow] getCurrencyData INFO: Актуальный курс eth: 2658.07193364 USD
[04-Jun-2025 19:13:51 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:13:52 Europe/Moscow] getCurrencyData INFO: Актуальный курс btc: 105230 USD
[04-Jun-2025 19:13:52 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:13:53 Europe/Moscow] getCurrencyData INFO: Актуальный курс trx: 0.27183292 USD
[04-Jun-2025 19:13:53 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=ltc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:13:54 Europe/Moscow] getCurrencyData INFO: Актуальный курс ltc: 89.61404265 USD
[04-Jun-2025 19:13:54 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=bch&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:13:55 Europe/Moscow] getCurrencyData INFO: Актуальный курс bch: 405.00139029 USD
[04-Jun-2025 19:13:56 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=xrp&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:13:56 Europe/Moscow] getCurrencyData INFO: Актуальный курс xrp: 2.24333985 USD
[04-Jun-2025 19:13:56 Europe/Moscow] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[04-Jun-2025 19:13:56 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[04-Jun-2025 19:13:56 Europe/Moscow] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[04-Jun-2025 19:13:57 Europe/Moscow] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk"}
[04-Jun-2025 19:13:57 Europe/Moscow] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[04-Jun-2025 19:13:57 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:13:58 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:06 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:14:06 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:14:07 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:14:07 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:14:08 Europe/Moscow] NOWPaymentsAPI CURL ERROR: OpenSSL SSL_connect: SSL_ERROR_SYSCALL in connection to api.nowpayments.io:443 
[04-Jun-2025 19:14:08 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.0001
[04-Jun-2025 19:14:08 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:14:08 Europe/Moscow] getCurrencyData INFO: eth - API минимум: 0.0001, USD: , монеты: 843, итого: 843
[04-Jun-2025 19:14:08 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:09 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:13 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:14:13 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:14:14 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[04-Jun-2025 19:14:14 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:14:14 Europe/Moscow] getCurrencyData INFO: btc - API минимум: 5.0E-6, USD: , монеты: 1680, итого: 1680
[04-Jun-2025 19:14:14 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:15 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:16 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:14:16 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:14:17 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[04-Jun-2025 19:14:17 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:14:17 Europe/Moscow] getCurrencyData INFO: usdttrc20 - API минимум: 8.58, USD: , монеты: 21714, итого: 21714
[04-Jun-2025 19:14:17 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:17 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:24 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:14:24 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:14:25 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[04-Jun-2025 19:14:25 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:14:25 Europe/Moscow] getCurrencyData INFO: trx - API минимум: 1, USD: , монеты: 960, итого: 960
[04-Jun-2025 19:14:25 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:26 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:26 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:14:26 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:14:28 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для ltc: 0.001
[04-Jun-2025 19:14:28 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:14:28 Europe/Moscow] getCurrencyData INFO: ltc - API минимум: 0.001, USD: , монеты: 1199, итого: 1199
[04-Jun-2025 19:14:28 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:30 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:31 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:14:31 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:14:32 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для bch: 0.001
[04-Jun-2025 19:14:32 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:14:32 Europe/Moscow] getCurrencyData INFO: bch - API минимум: 0.001, USD: , монеты: 1107, итого: 1107
[04-Jun-2025 19:14:32 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:33 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTM2MzUsImV4cCI6MTc0OTA1MzkzNX0.liMMwrW7ovaVtuzgnt560pdaDT7BGsz2H38SvF4IDhk","Content-Type: application\/json"]
[04-Jun-2025 19:14:36 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:14:36 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:14:37 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для xrp: 1
[04-Jun-2025 19:14:37 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:14:37 Europe/Moscow] getCurrencyData INFO: xrp - API минимум: 1, USD: , монеты: 2909, итого: 2909
[04-Jun-2025 19:30:52 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:30:52 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:36:21 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:36:21 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:36:21 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\debug_users_data.php on line 66
[04-Jun-2025 16:36:21 UTC] PHP Fatal error:  Uncaught TypeError: Cannot access offset of type array in isset or empty in D:\OSPanel\domains\argun-defolt.loc\debug_users_data.php:67
Stack trace:
#0 {main}
  thrown in D:\OSPanel\domains\argun-defolt.loc\debug_users_data.php on line 67
[04-Jun-2025 16:38:01 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:38:01 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:38:01 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:38:01 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:38:01 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:38:01 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:38:01 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 1820
[04-Jun-2025 16:38:01 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:38:01 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:38:23 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:38:23 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:38:43 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:38:43 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:39:02 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:39:02 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:39:03 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:04 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:05 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:06 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:07 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000139&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:08 Europe/Moscow] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[04-Jun-2025 19:39:08 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[04-Jun-2025 19:39:08 Europe/Moscow] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[04-Jun-2025 19:39:09 Europe/Moscow] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxNDYsImV4cCI6MTc0OTA1NTQ0Nn0.mUWBZAjN_UzjvJZ_C-M_S1lEp6hfDdwJPdOVaD1AU4I"}
[04-Jun-2025 19:39:09 Europe/Moscow] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[04-Jun-2025 19:39:09 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxNDYsImV4cCI6MTc0OTA1NTQ0Nn0.mUWBZAjN_UzjvJZ_C-M_S1lEp6hfDdwJPdOVaD1AU4I","Content-Type: application\/json"]
[04-Jun-2025 19:39:10 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxNDYsImV4cCI6MTc0OTA1NTQ0Nn0.mUWBZAjN_UzjvJZ_C-M_S1lEp6hfDdwJPdOVaD1AU4I","Content-Type: application\/json"]
[04-Jun-2025 19:39:10 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:39:10 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:11 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.0001
[04-Jun-2025 19:39:11 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:12 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxNDYsImV4cCI6MTc0OTA1NTQ0Nn0.mUWBZAjN_UzjvJZ_C-M_S1lEp6hfDdwJPdOVaD1AU4I","Content-Type: application\/json"]
[04-Jun-2025 19:39:13 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxNDYsImV4cCI6MTc0OTA1NTQ0Nn0.mUWBZAjN_UzjvJZ_C-M_S1lEp6hfDdwJPdOVaD1AU4I","Content-Type: application\/json"]
[04-Jun-2025 19:39:13 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:39:13 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:14 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[04-Jun-2025 19:39:14 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxNDYsImV4cCI6MTc0OTA1NTQ0Nn0.mUWBZAjN_UzjvJZ_C-M_S1lEp6hfDdwJPdOVaD1AU4I","Content-Type: application\/json"]
[04-Jun-2025 19:39:15 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxNDYsImV4cCI6MTc0OTA1NTQ0Nn0.mUWBZAjN_UzjvJZ_C-M_S1lEp6hfDdwJPdOVaD1AU4I","Content-Type: application\/json"]
[04-Jun-2025 19:39:15 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:39:15 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:16 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[04-Jun-2025 19:39:16 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:17 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxNDYsImV4cCI6MTc0OTA1NTQ0Nn0.mUWBZAjN_UzjvJZ_C-M_S1lEp6hfDdwJPdOVaD1AU4I","Content-Type: application\/json"]
[04-Jun-2025 19:39:18 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxNDYsImV4cCI6MTc0OTA1NTQ0Nn0.mUWBZAjN_UzjvJZ_C-M_S1lEp6hfDdwJPdOVaD1AU4I","Content-Type: application\/json"]
[04-Jun-2025 19:39:18 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:39:18 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:20 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[04-Jun-2025 19:39:20 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:39:20 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:39:25 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:39:25 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 281)
[04-Jun-2025 19:39:25 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 12345
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 12345
[04-Jun-2025 19:39:25 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:39:25 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 12345.
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 12345. Рефералов: 1
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 235)
[04-Jun-2025 19:39:25 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 5880288830
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 5880288830
[04-Jun-2025 19:39:25 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:39:25 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 5880288830.
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 5880288830. Рефералов: 1
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 168)
[04-Jun-2025 19:39:25 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 7479775119
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 7479775119
[04-Jun-2025 19:39:25 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:39:25 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 7479775119.
[04-Jun-2025 19:39:25 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 7479775119. Рефералов: 0
[04-Jun-2025 19:39:46 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:47 Europe/Moscow] getCurrencyData INFO: Актуальный курс eth: 2647.22125171 USD
[04-Jun-2025 19:39:47 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:48 Europe/Moscow] getCurrencyData INFO: Актуальный курс btc: 105130 USD
[04-Jun-2025 19:39:48 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:49 Europe/Moscow] getCurrencyData INFO: Актуальный курс trx: 0.27263286 USD
[04-Jun-2025 19:39:49 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=ltc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:49 Europe/Moscow] getCurrencyData INFO: Актуальный курс ltc: 89.57996453 USD
[04-Jun-2025 19:39:49 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=bch&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:50 Europe/Moscow] getCurrencyData INFO: Актуальный курс bch: 405.26056784 USD
[04-Jun-2025 19:39:50 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=xrp&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:51 Europe/Moscow] getCurrencyData INFO: Актуальный курс xrp: 2.24114269 USD
[04-Jun-2025 19:39:51 Europe/Moscow] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[04-Jun-2025 19:39:51 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[04-Jun-2025 19:39:51 Europe/Moscow] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[04-Jun-2025 19:39:51 Europe/Moscow] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js"}
[04-Jun-2025 19:39:52 Europe/Moscow] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[04-Jun-2025 19:39:52 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:39:54 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:39:54 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:39:54 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:55 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.0001
[04-Jun-2025 19:39:55 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:39:55 Europe/Moscow] getCurrencyData INFO: eth - API минимум: 0.0001, USD: , монеты: 842, итого: 842
[04-Jun-2025 19:39:55 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:39:56 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:39:57 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:39:57 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:39:59 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[04-Jun-2025 19:39:59 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:39:59 Europe/Moscow] getCurrencyData INFO: btc - API минимум: 5.0E-6, USD: , монеты: 1679, итого: 1679
[04-Jun-2025 19:40:00 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:00 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:01 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:40:01 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:40:02 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[04-Jun-2025 19:40:03 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:40:03 Europe/Moscow] getCurrencyData INFO: usdttrc20 - API минимум: 8.58, USD: , монеты: 21714, итого: 21714
[04-Jun-2025 19:40:03 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:04 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:05 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:40:05 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:40:06 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[04-Jun-2025 19:40:06 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:40:06 Europe/Moscow] getCurrencyData INFO: trx - API минимум: 1, USD: , монеты: 961, итого: 961
[04-Jun-2025 19:40:06 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:07 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:08 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:40:08 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:40:09 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для ltc: 0.001
[04-Jun-2025 19:40:09 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:40:09 Europe/Moscow] getCurrencyData INFO: ltc - API минимум: 0.001, USD: , монеты: 1199, итого: 1199
[04-Jun-2025 19:40:09 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:10 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:10 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:40:10 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:40:12 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для bch: 0.001
[04-Jun-2025 19:40:12 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:40:12 Europe/Moscow] getCurrencyData INFO: bch - API минимум: 0.001, USD: , монеты: 1107, итого: 1107
[04-Jun-2025 19:40:12 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:12 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTUxODksImV4cCI6MTc0OTA1NTQ4OX0.oHj3yaD5EyWMN5wJ-x_O2CKFFdIXtVj_O6JlWFb1-Js","Content-Type: application\/json"]
[04-Jun-2025 19:40:13 Europe/Moscow] NOWPaymentsAPI CURL ERROR: OpenSSL SSL_connect: SSL_ERROR_SYSCALL in connection to api.nowpayments.io:443 
[04-Jun-2025 19:40:13 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:40:14 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для xrp: 1
[04-Jun-2025 19:40:14 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:40:14 Europe/Moscow] getCurrencyData INFO: xrp - API минимум: 1, USD: , монеты: 2907, итого: 2907
[04-Jun-2025 16:47:59 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:47:59 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:48:55 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:48:55 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:48:55 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:48:55 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:48:55 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:48:55 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:48:55 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 1820
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:56:54 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 2476
[04-Jun-2025 19:56:54 Europe/Moscow] getUserData INFO: Получен initData (длина: 316)
[04-Jun-2025 19:56:54 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:56:54 Europe/Moscow] getUserData WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:56:54 Europe/Moscow] getUserData INFO: Упрощенная валидация прошла для пользователя 999888778
[04-Jun-2025 19:56:54 Europe/Moscow] getUserData INFO: initData успешно валидирован для пользователя 999888778
[04-Jun-2025 19:56:54 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:56:54 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:56:54 Europe/Moscow] getUserData INFO: Данные пользователей загружены для user 999888778.
[04-Jun-2025 19:56:54 Europe/Moscow] db_mock INFO: User 999888778 created with FULL structure. Current userData count: 5
[04-Jun-2025 19:56:54 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 999888778
[04-Jun-2025 19:56:54 Europe/Moscow] getUserData INFO: Детали пользователя 999888778 получены. Баланс: 0
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 3139
[04-Jun-2025 19:56:54 Europe/Moscow] getUserData INFO: Данные пользователя 999888778 сохранены (на случай создания).
[04-Jun-2025 19:56:54 Europe/Moscow] getUserData INFO: Успешный ответ отправлен для user 999888778.
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:56:54 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 2476
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:56:54 Europe/Moscow] registerReferral INFO: Получен initData (длина: 316) и referrerId: 999888777
[04-Jun-2025 19:56:54 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:56:54 Europe/Moscow] registerReferral WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:56:54 Europe/Moscow] registerReferral INFO: Упрощенная валидация прошла для пользователя 999888778
[04-Jun-2025 19:56:54 Europe/Moscow] registerReferral INFO: initData успешно валидирован для пользователя 999888778
[04-Jun-2025 19:56:54 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:56:54 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:56:54 Europe/Moscow] registerReferral INFO: Данные пользователей загружены для user 999888778 и referrer 999888777.
[04-Jun-2025 19:56:54 Europe/Moscow] db_mock INFO: User 999888778 created with FULL structure and referrer 999888777 Current userData count: 5
[04-Jun-2025 19:56:54 Europe/Moscow] db_mock INFO: Updated Telegram user data for user 999888778
[04-Jun-2025 19:56:54 Europe/Moscow] registerReferral INFO: Создан новый пользователь 999888778 с реферером 999888777
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData INFO: json_encode успешен.
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 19:56:54 Europe/Moscow] saveUserData SUCCESS: Данные сохранены. Записано байт: 3176
[04-Jun-2025 19:56:54 Europe/Moscow] registerReferral INFO: Данные пользователя 999888778 и реферера 999888777 сохранены.
[04-Jun-2025 19:56:54 Europe/Moscow] registerReferral INFO: Успешный ответ отправлен для user 999888778 и referrer 999888777.
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 281)
[04-Jun-2025 19:56:54 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 999888777
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 999888777
[04-Jun-2025 19:56:54 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:56:54 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 999888777.
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 999888777. Рефералов: 1
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: Получен initData (длина: 316)
[04-Jun-2025 19:56:54 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: Упрощенная валидация прошла для пользователя 999888778
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: initData успешно валидирован для пользователя 999888778
[04-Jun-2025 19:56:54 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:56:54 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: Данные пользователей загружены для user 999888778.
[04-Jun-2025 19:56:54 Europe/Moscow] getReferralStats INFO: Успешный ответ отправлен для user 999888778. Рефералов: 0
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: Начало сохранения данных.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: json_encode успешен.
[04-Jun-2025 16:56:54 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[04-Jun-2025 16:56:54 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 1820
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 16:56:54 UTC] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:57:10 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:57:10 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:57:27 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[04-Jun-2025 19:57:27 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[04-Jun-2025 19:57:28 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:29 Europe/Moscow] getCurrencyData INFO: Актуальный курс eth: 2651.92841145 USD
[04-Jun-2025 19:57:29 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:30 Europe/Moscow] getCurrencyData INFO: Актуальный курс btc: 105350 USD
[04-Jun-2025 19:57:30 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:31 Europe/Moscow] getCurrencyData INFO: Актуальный курс trx: 0.27298791 USD
[04-Jun-2025 19:57:31 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=ltc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:32 Europe/Moscow] getCurrencyData INFO: Актуальный курс ltc: 89.53829817 USD
[04-Jun-2025 19:57:32 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=bch&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:32 Europe/Moscow] getCurrencyData INFO: Актуальный курс bch: 406.14197603 USD
[04-Jun-2025 19:57:32 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=xrp&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:33 Europe/Moscow] getCurrencyData INFO: Актуальный курс xrp: 2.24000657 USD
[04-Jun-2025 19:57:33 Europe/Moscow] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[04-Jun-2025 19:57:33 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[04-Jun-2025 19:57:33 Europe/Moscow] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[04-Jun-2025 19:57:34 Europe/Moscow] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0"}
[04-Jun-2025 19:57:34 Europe/Moscow] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[04-Jun-2025 19:57:34 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:35 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:35 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:57:35 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:36 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.0001
[04-Jun-2025 19:57:36 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:57:36 Europe/Moscow] getCurrencyData INFO: eth - API минимум: 0.0001, USD: , монеты: 843, итого: 843
[04-Jun-2025 19:57:36 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:40 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:41 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:57:41 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:42 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[04-Jun-2025 19:57:42 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:57:42 Europe/Moscow] getCurrencyData INFO: btc - API минимум: 5.0E-6, USD: , монеты: 1680, итого: 1680
[04-Jun-2025 19:57:42 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:43 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:44 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:57:44 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:45 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[04-Jun-2025 19:57:45 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:57:45 Europe/Moscow] getCurrencyData INFO: usdttrc20 - API минимум: 8.58, USD: , монеты: 21714, итого: 21714
[04-Jun-2025 19:57:45 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:46 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:47 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:57:47 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:48 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[04-Jun-2025 19:57:48 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:57:48 Europe/Moscow] getCurrencyData INFO: trx - API минимум: 1, USD: , монеты: 961, итого: 961
[04-Jun-2025 19:57:48 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:49 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:50 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:57:50 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:51 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для ltc: 0.001
[04-Jun-2025 19:57:51 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:57:51 Europe/Moscow] getCurrencyData INFO: ltc - API минимум: 0.001, USD: , монеты: 1199, итого: 1199
[04-Jun-2025 19:57:51 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:52 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:52 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:57:52 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:53 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для bch: 0.001
[04-Jun-2025 19:57:53 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:57:53 Europe/Moscow] getCurrencyData INFO: bch - API минимум: 0.001, USD: , монеты: 1108, итого: 1108
[04-Jun-2025 19:57:53 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:54 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDkwNTYyNTIsImV4cCI6MTc0OTA1NjU1Mn0.I7luQJgejljbCxDYWZLJtj_JoR_cmXmIHqnQHSmcDM0","Content-Type: application\/json"]
[04-Jun-2025 19:57:55 Europe/Moscow] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[04-Jun-2025 19:57:55 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[04-Jun-2025 19:57:56 Europe/Moscow] NOWPaymentsAPI INFO: Используем известный минимум для xrp: 1
[04-Jun-2025 19:57:56 Europe/Moscow] PHP Warning:  Undefined variable $minInUsd in D:\OSPanel\domains\argun-defolt.loc\api\getCurrencyData.php on line 132
[04-Jun-2025 19:57:56 Europe/Moscow] getCurrencyData INFO: xrp - API минимум: 1, USD: , монеты: 2906, итого: 2906
