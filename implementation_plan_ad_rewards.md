# План реализации раздельных наград за рекламу

## Обзор изменений
- **Цель**: Реализовать раздельные награды для разных типов рекламы
- **Требования**:
  - Видеореклама (rewarded_video): 1 монета
  - Баннеры (native_banner, interstitial): 10 монет
  - Настройки через админку
  - Обратная совместимость
- **Затронутые файлы**:
  - `api/recordAdView.php` - логика начисления
  - `api/admin/settings.php` - панель управления
  - `main.js` - фронтенд

## Детали реализации

### 1. Изменение логики начисления наград (`api/recordAdView.php`)

```php:api/recordAdView.php
// Заменить существующую логику на строках 47-48:
$adType = $input['adType'] ?? 'default';
$rewardAmount = 10; // Значение по умолчанию для обратной совместимости

if ($adType === 'native_banner' && defined('AD_REWARD_NATIVE_BANNER')) {
    $rewardAmount = AD_REWARD_NATIVE_BANNER;
} elseif ($adType === 'interstitial' && defined('AD_REWARD_INTERSTITIAL')) {
    $rewardAmount = AD_REWARD_INTERSTITIAL;
} elseif ($adType === 'rewarded_video' && defined('AD_REWARD_REWARDED_VIDEO')) {
    $rewardAmount = AD_REWARD_REWARDED_VIDEO;
}
```

### 2. Обновление админ-панели (`api/admin/settings.php`)

#### Добавление полей для настройки наград
```php:api/admin/settings.php
<!-- После существующего поля ad_view_reward -->
<div class="mb-3">
    <label class="form-label">Награды за типы рекламы</label>
    <div class="row">
        <div class="col-md-4">
            <label for="ad_reward_native_banner" class="form-label">Баннер (монет)</label>
            <input type="number" class="form-control" id="ad_reward_native_banner" 
                   name="ad_reward_native_banner" 
                   value="<?= defined('AD_REWARD_NATIVE_BANNER') ? AD_REWARD_NATIVE_BANNER : 10 ?>" 
                   min="1" required>
        </div>
        <div class="col-md-4">
            <label for="ad_reward_interstitial" class="form-label">Полноэкранная (монет)</label>
            <input type="number" class="form-control" id="ad_reward_interstitial" 
                   name="ad_reward_interstitial" 
                   value="<?= defined('AD_REWARD_INTERSTITIAL') ? AD_REWARD_INTERSTITIAL : 10 ?>" 
                   min="1" required>
        </div>
        <div class="col-md-4">
            <label for="ad_reward_rewarded_video" class="form-label">Видео (монет)</label>
            <input type="number" class="form-control" id="ad_reward_rewarded_video" 
                   name="ad_reward_rewarded_video" 
                   value="<?= defined('AD_REWARD_REWARDED_VIDEO') ? AD_REWARD_REWARDED_VIDEO : 1 ?>" 
                   min="1" required>
        </div>
    </div>
</div>
```

#### Логика сохранения настроек
```php:api/admin/settings.php
// Добавить после обработки ad_view_reward
if (isset($_POST['ad_reward_native_banner']) && is_numeric($_POST['ad_reward_native_banner'])) {
    $value = intval($_POST['ad_reward_native_banner']);
    $pattern = "/define\('AD_REWARD_NATIVE_BANNER',\s*\d+\);/";
    $replacement = "define('AD_REWARD_NATIVE_BANNER', $value);";
    $configContent = preg_replace($pattern, $replacement, $configContent);
    $updated = true;
}

// Повторить для interstitial и rewarded_video
```

### 3. Обновление фронтенда (`main.js`)
```js:main.js
// Удалить хардкод наград (строки 71-75)
- ad_rewards: {
-   native_banner: 10,
-   interstitial: 10,
-   rewarded_video: 1
- },
```

## Тестирование

### Сценарии проверки
1. **Базовый функционал**:
   - Просмотр баннера → +10 монет
   - Просмотр видео → +1 монета
   - Просмотр без типа → +10 монет (совместимость)

2. **Админ-панель**:
   - Изменение значений наград
   - Сохранение настроек
   - Проверка применения новых значений

3. **Граничные случаи**:
   - Несуществующий тип рекламы
   - Отрицательные значения в настройках
   - Пустые значения

## Порядок внедрения
1. Создать бэкап системы
2. Применить изменения кода
3. Провести тестирование:
   ```sh
   # Проверка баннерной рекламы
   curl -X POST -H "Content-Type: application/json" -d '{"initData":"...","adType":"native_banner"}' http://localhost/api/recordAdView.php

   # Проверка видеорекламы
   curl -X POST -H "Content-Type: application/json" -d '{"initData":"...","adType":"rewarded_video"}' http://localhost/api/recordAdView.php
   ```
4. Проверить обновление баланса в UI
5. Обновить настройки через админку и повторить проверки

## Риски и митигация
| Риск | Митигация |
|------|-----------|
| Потеря обратной совместимости | Сохранение значения по умолчанию 10 монет |
| Ошибка при сохранении настроек | Валидация вводимых значений |
| Несовпадение типов рекламы | Стандартизация имен типов в коде |