<?php
/**
 * Диагностика проблем с реферальной системой
 */

echo "=== ДИАГНОСТИКА ПРОБЛЕМ РЕФЕРАЛЬНОЙ СИСТЕМЫ ===\n\n";

// Подключаем зависимости
require_once 'api/config.php';
require_once 'api/db_mock.php';

echo "1. Анализ текущих данных:\n";

$userData = loadUserData();

foreach ($userData as $userId => $user) {
    $displayName = '';
    if (!empty($user['username'])) {
        $displayName = '@' . $user['username'];
    } elseif (!empty($user['first_name']) || !empty($user['last_name'])) {
        $displayName = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
    } else {
        $displayName = "ID: $userId";
    }
    
    echo "  👤 $displayName (ID: $userId)\n";
    echo "    Баланс: " . ($user['balance'] ?? 0) . " монет\n";
    echo "    Реферер: " . ($user['referrer_id'] ?? 'отсутствует') . "\n";
    echo "    Рефералов: " . count($user['referrals'] ?? []) . "\n";
    if (!empty($user['referrals'])) {
        echo "    Список рефералов: " . implode(', ', $user['referrals']) . "\n";
    }
    echo "    Заработок с рефералов: " . ($user['referral_earnings'] ?? 0) . " монет\n";
    echo "    ────────────────────────────────────\n";
}

echo "\n2. Проверка проблемы с пользователем 5880288830 (Альтер Эго):\n";

$alterId = 5880288830;
$dimaId = 7479775119;

if (isset($userData[$alterId])) {
    $alter = $userData[$alterId];
    echo "  Альтер Эго найден:\n";
    echo "    Реферер: " . ($alter['referrer_id'] ?? 'отсутствует') . "\n";
    echo "    Баланс: " . ($alter['balance'] ?? 0) . " монет\n";
    
    if ($alter['referrer_id'] == $dimaId) {
        echo "    ✅ Правильно ссылается на Диму как реферера\n";
    } else {
        echo "    ❌ НЕ ссылается на Диму как реферера\n";
    }
} else {
    echo "  ❌ Альтер Эго НЕ найден в системе\n";
}

echo "\n3. Проверка Димы как реферера:\n";

if (isset($userData[$dimaId])) {
    $dima = $userData[$dimaId];
    echo "  Дима найден:\n";
    echo "    Баланс: " . ($dima['balance'] ?? 0) . " монет\n";
    echo "    Рефералов: " . count($dima['referrals'] ?? []) . "\n";
    echo "    Список рефералов: " . implode(', ', $dima['referrals'] ?? []) . "\n";
    echo "    Заработок с рефералов: " . ($dima['referral_earnings'] ?? 0) . " монет\n";
    
    if (in_array($alterId, $dima['referrals'] ?? [])) {
        echo "    ✅ Альтер Эго есть в списке рефералов\n";
    } else {
        echo "    ❌ Альтер Эго НЕТ в списке рефералов\n";
    }
} else {
    echo "  ❌ Дима НЕ найден в системе\n";
}

echo "\n4. Тестирование API registerReferral.php:\n";

// Удаляем Альтера Эго для чистого теста
if (isset($userData[$alterId])) {
    // Удаляем из списка рефералов Димы
    if (isset($userData[$dimaId]['referrals'])) {
        $userData[$dimaId]['referrals'] = array_filter(
            $userData[$dimaId]['referrals'], 
            function($id) use ($alterId) { return $id != $alterId; }
        );
        $userData[$dimaId]['referrals_count'] = count($userData[$dimaId]['referrals']);
    }
    
    unset($userData[$alterId]);
    saveUserData($userData);
    echo "  🗑️ Альтер Эго удален для чистого теста\n";
}

// Тестируем регистрацию через API
$testInitData = 'user=' . urlencode(json_encode([
    'id' => $alterId,
    'username' => 'alter_mega_ego',
    'first_name' => 'Альтер',
    'last_name' => 'Эго'
])) . '&auth_date=' . time() . '&hash=test_hash';

echo "  📡 Отправляем запрос к registerReferral.php...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://argun-defolt.loc/api/registerReferral.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'initData' => $testInitData,
    'referrerId' => $dimaId
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "    HTTP код: $httpCode\n";
echo "    Ответ: " . $response . "\n";

if ($httpCode === 200) {
    $result = json_decode($response, true);
    if ($result['success'] ?? false) {
        echo "    ✅ API вернул успех\n";
    } else {
        echo "    ❌ API вернул ошибку: " . ($result['message'] ?? 'Неизвестно') . "\n";
    }
} else {
    echo "    ❌ HTTP ошибка\n";
}

echo "\n5. Проверка результата после API вызова:\n";

$updatedData = loadUserData();

// Проверяем Альтера Эго
if (isset($updatedData[$alterId])) {
    $alter = $updatedData[$alterId];
    echo "  ✅ Альтер Эго создан:\n";
    echo "    Баланс: " . ($alter['balance'] ?? 0) . " монет (должно быть 10)\n";
    echo "    Реферер: " . ($alter['referrer_id'] ?? 'отсутствует') . " (должно быть $dimaId)\n";
    
    if ($alter['balance'] >= 10) {
        echo "    ✅ Баланс корректный\n";
    } else {
        echo "    ❌ Баланс неверный - не получил 10 монет\n";
    }
    
    if ($alter['referrer_id'] == $dimaId) {
        echo "    ✅ Реферер установлен корректно\n";
    } else {
        echo "    ❌ Реферер неверный\n";
    }
} else {
    echo "  ❌ Альтер Эго НЕ создан\n";
}

// Проверяем Диму
if (isset($updatedData[$dimaId])) {
    $dima = $updatedData[$dimaId];
    echo "  📊 Дима после регистрации реферала:\n";
    echo "    Баланс: " . ($dima['balance'] ?? 0) . " монет\n";
    echo "    Рефералов: " . count($dima['referrals'] ?? []) . "\n";
    echo "    Список рефералов: " . implode(', ', $dima['referrals'] ?? []) . "\n";
    echo "    Заработок с рефералов: " . ($dima['referral_earnings'] ?? 0) . " монет\n";
    
    if (in_array($alterId, $dima['referrals'] ?? [])) {
        echo "    ✅ Альтер Эго добавлен в список рефералов\n";
    } else {
        echo "    ❌ Альтер Эго НЕ добавлен в список рефералов\n";
    }
}

echo "\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===\n";
echo "Проблемы будут исправлены в следующих шагах.\n";
?>
