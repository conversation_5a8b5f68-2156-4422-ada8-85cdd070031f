<?php
/**
 * api/getCurrencyData.php
 * API для получения актуальных данных о валютах с минимумами и комиссиями
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Подключаем конфигурацию
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

try {
    // Создаем экземпляр API для получения актуальных минимумов
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Базовые данные о валютах (синхронизированы с main.js)
    $currencyData = [
        'eth' => [
            'name' => 'Ethereum (ETH)',
            'minCoins' => 250, // Будет обновлено из API
            'networkFee' => 0.25,
            'status' => 'best'
        ],
        'btc' => [
            'name' => 'Bitcoin (BTC)',
            'minCoins' => 5, // Будет обновлено из API
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'usdttrc20' => [
            'name' => 'USDT (TRC20)',
            'minCoins' => 8580, // Будет обновлено из API
            'networkFee' => 5.58,
            'status' => 'expensive'
        ],
        'trx' => [
            'name' => 'TRON (TRX)',
            'minCoins' => 1000, // Будет обновлено из API
            'networkFee' => 0.30,
            'status' => 'good'
        ],
        'ltc' => [
            'name' => 'Litecoin (LTC)',
            'minCoins' => 1000, // Будет обновлено из API
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'bch' => [
            'name' => 'Bitcoin Cash (BCH)',
            'minCoins' => 1000, // Будет обновлено из API
            'networkFee' => 0.30,
            'status' => 'good'
        ],
        'xrp' => [
            'name' => 'Ripple (XRP)',
            'minCoins' => 1000, // Будет обновлено из API
            'networkFee' => 0.20,
            'status' => 'good'
        ]
    ];
    
    // Обновляем минимумы из API NOWPayments
    foreach ($currencyData as $currency => &$data) {
        try {
            $apiMinimum = $api->getMinWithdrawalAmount($currency);
            if ($apiMinimum !== null) {
                // Конвертируем минимум из криптовалюты в монеты
                $minInUsd = $apiMinimum;
                
                // Для криптовалют нужно конвертировать в USD
                if ($currency !== 'usdttrc20') {
                    try {
                        $estimate = $api->getEstimateAmount($apiMinimum, $currency, 'usd');
                        if ($estimate && isset($estimate['estimated_amount'])) {
                            $minInUsd = $estimate['estimated_amount'];
                        }
                    } catch (Exception $e) {
                        error_log("getCurrencyData WARNING: Не удалось конвертировать минимум {$currency} в USD: " . $e->getMessage());
                        // Используем приблизительные курсы
                        switch ($currency) {
                            case 'eth':
                                $minInUsd = $apiMinimum * 2630; // ETH ≈ $2630
                                break;
                            case 'btc':
                                $minInUsd = $apiMinimum * 40000; // BTC ≈ $40000
                                break;
                            case 'trx':
                                $minInUsd = $apiMinimum * 0.12; // TRX ≈ $0.12
                                break;
                            default:
                                $minInUsd = $apiMinimum; // Для остальных используем как есть
                        }
                    }
                }
                
                // Конвертируем USD в монеты (1 монета = $0.001)
                $minInCoins = ceil($minInUsd / CONVERSION_RATE);
                
                // Обновляем минимум, но не делаем его меньше текущего значения
                $data['minCoins'] = max($data['minCoins'], $minInCoins);
                
                error_log("getCurrencyData INFO: {$currency} - API минимум: {$apiMinimum}, USD: {$minInUsd}, монеты: {$minInCoins}");
            }
        } catch (Exception $e) {
            error_log("getCurrencyData WARNING: Ошибка получения минимума для {$currency}: " . $e->getMessage());
            // Оставляем значение по умолчанию
        }
    }
    
    // Добавляем информацию о том, показывать ли комиссии
    $showFees = defined('SHOW_FEES_TO_USER') ? SHOW_FEES_TO_USER : true;
    
    // Возвращаем обновленные данные
    echo json_encode([
        'success' => true,
        'currencies' => $currencyData,
        'show_fees' => $showFees,
        'conversion_rate' => CONVERSION_RATE,
        'updated_at' => time(),
        'message' => 'Данные о валютах успешно загружены'
    ]);
    
} catch (Exception $e) {
    error_log("getCurrencyData ERROR: " . $e->getMessage());
    
    // В случае ошибки возвращаем базовые данные
    $fallbackData = [
        'eth' => [
            'name' => 'Ethereum (ETH)',
            'minCoins' => 250,
            'networkFee' => 0.25,
            'status' => 'best'
        ],
        'btc' => [
            'name' => 'Bitcoin (BTC)',
            'minCoins' => 5,
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'usdttrc20' => [
            'name' => 'USDT (TRC20)',
            'minCoins' => 8580,
            'networkFee' => 5.58,
            'status' => 'expensive'
        ],
        'trx' => [
            'name' => 'TRON (TRX)',
            'minCoins' => 1000,
            'networkFee' => 0.30,
            'status' => 'good'
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'currencies' => $fallbackData,
        'show_fees' => true,
        'conversion_rate' => 0.001,
        'updated_at' => time(),
        'message' => 'Использованы данные по умолчанию из-за ошибки API',
        'error' => $e->getMessage()
    ]);
}
?>
