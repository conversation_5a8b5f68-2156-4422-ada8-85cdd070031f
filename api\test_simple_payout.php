<?php
/**
 * Тест простой выплаты без дополнительных полей
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "=== ТЕСТ ПРОСТОЙ ВЫПЛАТЫ ===\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Тестируем разные форматы запроса
$testAddress = 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE';
$testCurrency = 'usdttrc20';
$testAmount = 0.5;

echo "Параметры:\n";
echo "- Адрес: {$testAddress}\n";
echo "- Валюта: {$testCurrency}\n";
echo "- Сумма: {$testAmount}\n\n";

// Тест 1: Минимальный формат
echo "1. Тест минимального формата:\n";
$url = NOWPAYMENTS_API_URL . '/payout';
$data1 = [
    'withdrawals' => [
        [
            'address' => $testAddress,
            'currency' => $testCurrency,
            'amount' => $testAmount
        ]
    ]
];

echo "Данные: " . json_encode($data1) . "\n";
$result1 = $api->makeRequest('POST', $url, $data1, 'both');
echo "Результат: " . json_encode($result1) . "\n\n";

// Тест 2: С callback URL
echo "2. Тест с callback URL:\n";
$data2 = [
    'withdrawals' => [
        [
            'address' => $testAddress,
            'currency' => $testCurrency,
            'amount' => $testAmount,
            'ipn_callback_url' => 'https://app.uniqpaid.com/test2/api/withdrawal_callback.php'
        ]
    ]
];

echo "Данные: " . json_encode($data2) . "\n";
$result2 = $api->makeRequest('POST', $url, $data2, 'both');
echo "Результат: " . json_encode($result2) . "\n\n";

// Тест 3: Другой endpoint
echo "3. Тест другого endpoint (/mass-payouts):\n";
$url3 = NOWPAYMENTS_API_URL . '/mass-payouts';
$data3 = [
    'payouts' => [
        [
            'address' => $testAddress,
            'currency' => $testCurrency,
            'amount' => $testAmount
        ]
    ]
];

echo "URL: {$url3}\n";
echo "Данные: " . json_encode($data3) . "\n";
$result3 = $api->makeRequest('POST', $url3, $data3, 'both');
echo "Результат: " . json_encode($result3) . "\n\n";

// Тест 4: Проверим доступные endpoints
echo "4. Проверка доступных валют:\n";
$currencies = $api->getAvailableCurrencies();
echo "Валюты: " . json_encode($currencies) . "\n\n";

// Тест 5: Проверим статус API
echo "5. Проверка статуса API:\n";
$statusUrl = NOWPAYMENTS_API_URL . '/status';
$status = $api->makeRequest('GET', $statusUrl, null, 'x-api-key');
echo "Статус: " . json_encode($status) . "\n\n";

echo "=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
