<?php
/**
 * Тест правильного формата данных для NOWPayments API
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🔧 ТЕСТ ФОРМАТА ДАННЫХ ДЛЯ NOWPayments API\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

echo "📋 Тестируем различные форматы данных для создания выплат...\n\n";

// Тест 1: Простой формат (как в старой версии)
echo "1️⃣ Тест простого формата:\n";
$simpleData = [
    'withdrawals' => [
        [
            'address' => '**********************************',
            'currency' => 'btc',
            'amount' => 0.000005
        ]
    ]
];

echo "Данные: " . json_encode($simpleData, JSON_PRETTY_PRINT) . "\n";

// Тест 2: Формат с дополнительными полями
echo "\n2️⃣ Тест расширенного формата:\n";
$extendedData = [
    'withdrawals' => [
        [
            'address' => '**********************************',
            'currency' => 'btc',
            'amount' => 0.000005,
            'ipn_callback_url' => 'https://app.uniqpaid.com/test2/api/withdrawal_callback.php',
            'fee_paid_by_user' => true
        ]
    ]
];

echo "Данные: " . json_encode($extendedData, JSON_PRETTY_PRINT) . "\n";

// Тест 3: Формат без массива withdrawals
echo "\n3️⃣ Тест прямого формата:\n";
$directData = [
    'address' => '**********************************',
    'currency' => 'btc',
    'amount' => 0.000005,
    'ipn_callback_url' => 'https://app.uniqpaid.com/test2/api/withdrawal_callback.php',
    'fee_paid_by_user' => true
];

echo "Данные: " . json_encode($directData, JSON_PRETTY_PRINT) . "\n";

// Тест 4: Проверяем, что отправляет наш текущий код
echo "\n4️⃣ Текущий формат нашего кода:\n";

// Создаем тестовый метод для проверки
class TestAPI extends NOWPaymentsAPI {
    public function testCurrentFormat($address, $currency, $amount) {
        $url = $this->apiUrl . '/payout';
        $data = [
            'withdrawals' => [
                [
                    'address' => $address,
                    'currency' => $currency,
                    'amount' => (float)$amount,
                    'ipn_callback_url' => 'https://app.uniqpaid.com/test2/api/withdrawal_callback.php',
                    'fee_paid_by_user' => true
                ]
            ]
        ];
        
        echo "URL: {$url}\n";
        echo "Данные: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";
        
        return $data;
    }
}

$testApi = new TestAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
$currentFormat = $testApi->testCurrentFormat('**********************************', 'btc', 0.000005);

echo "\n" . str_repeat("=", 60) . "\n";
echo "🔍 АНАЛИЗ ПРОБЛЕМЫ\n";
echo str_repeat("=", 60) . "\n\n";

echo "❌ Ошибка: 'withdrawals[0] does not match any of the allowed types'\n\n";

echo "🤔 Возможные причины:\n";
echo "1. Неправильная структура массива withdrawals\n";
echo "2. Неправильные типы данных (amount должен быть float)\n";
echo "3. Отсутствие обязательных полей\n";
echo "4. Неправильный endpoint URL\n";
echo "5. Проблемы с авторизацией\n\n";

echo "🔧 Попробуем разные варианты исправления:\n\n";

// Проверяем баланс для понимания доступных валют
echo "💰 Проверяем баланс аккаунта:\n";
$balance = $api->getAccountBalance();
if ($balance) {
    foreach ($balance as $currency => $data) {
        if (is_array($data) && isset($data['amount']) && $data['amount'] > 0) {
            echo "- {$currency}: {$data['amount']}\n";
        }
    }
} else {
    echo "❌ Не удалось получить баланс\n";
}

echo "\n📚 Рекомендации для исправления:\n";
echo "1. Проверить актуальную документацию NOWPayments API\n";
echo "2. Убедиться в правильности типов данных\n";
echo "3. Проверить обязательные поля\n";
echo "4. Протестировать с минимальным набором полей\n";
echo "5. Проверить совместимость адреса с валютой\n\n";

echo "🚀 Следующие шаги:\n";
echo "1. Исправить структуру данных в createPayoutWithFeeHandling()\n";
echo "2. Протестировать с простейшим форматом\n";
echo "3. Постепенно добавлять дополнительные поля\n";
echo "4. Проверить результат\n\n";

echo "✅ Тест завершен. Переходим к исправлению...\n";
?>
