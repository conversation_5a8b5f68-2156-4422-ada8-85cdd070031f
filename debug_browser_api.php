<?php
/**
 * Отладка API для браузера
 */

echo "=== ОТЛАДКА API ДЛЯ БРАУЗЕРА ===\n\n";

// Подключаем зависимости
require_once 'api/config.php';
require_once 'api/db_mock.php';

echo "1. Проверяем данные пользователей:\n";

$userData = loadUserData();

foreach ($userData as $userId => $user) {
    $displayName = '';
    if (!empty($user['username'])) {
        $displayName = '@' . $user['username'];
    } elseif (!empty($user['first_name']) || !empty($user['last_name'])) {
        $displayName = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
    } else {
        $displayName = "ID: $userId";
    }
    
    echo "  👤 $displayName (ID: $userId)\n";
    echo "    Заработок с рефералов: " . ($user['referral_earnings'] ?? 0) . " монет\n";
    echo "    Рефералов: " . count($user['referrals'] ?? []) . "\n";
    if (!empty($user['referrals'])) {
        echo "    Список рефералов: " . implode(', ', $user['referrals']) . "\n";
    }
    echo "    ────────────────────────────────────\n";
}

echo "\n2. Создаем тестовый файл для проверки API:\n";

// Создаем простой тестовый файл для проверки API
$testApiContent = '<?php
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    exit(0);
}

require_once "config.php";
require_once "db_mock.php";

$userData = loadUserData();

echo json_encode([
    "debug" => true,
    "users_count" => count($userData),
    "users_with_earnings" => array_filter($userData, function($user) {
        return ($user["referral_earnings"] ?? 0) > 0;
    }),
    "timestamp" => time()
]);
?>';

file_put_contents('api/debug_api.php', $testApiContent);
echo "  ✅ Создан файл api/debug_api.php для тестирования\n";

echo "\n3. Проверяем содержимое getReferralStats.php:\n";

$apiContent = file_get_contents('api/getReferralStats.php');

// Ищем ключевые части
if (strpos($apiContent, '$totalEarned') !== false) {
    echo "  ✅ Переменная \$totalEarned найдена\n";
} else {
    echo "  ❌ Переменная \$totalEarned НЕ найдена\n";
}

if (strpos($apiContent, '"totalEarned"') !== false) {
    echo "  ✅ Поле totalEarned в JSON найдено\n";
} else {
    echo "  ❌ Поле totalEarned в JSON НЕ найдено\n";
}

if (strpos($apiContent, 'referral_earnings') !== false) {
    echo "  ✅ Обращение к referral_earnings найдено\n";
} else {
    echo "  ❌ Обращение к referral_earnings НЕ найдено\n";
}

echo "\n4. Проверяем main.js:\n";

$jsContent = file_get_contents('main.js');

if (strpos($jsContent, 'data.totalEarned') !== false) {
    echo "  ✅ Использование data.totalEarned найдено\n";
} else {
    echo "  ❌ Использование data.totalEarned НЕ найдено\n";
}

// Ищем строку с referralEarningsEl
$lines = explode("\n", $jsContent);
foreach ($lines as $lineNum => $line) {
    if (strpos($line, 'referralEarningsEl.textContent') !== false) {
        echo "  📝 Строка " . ($lineNum + 1) . ": " . trim($line) . "\n";
    }
}

echo "\n5. Создаем тестовую страницу для проверки API:\n";

$testPageContent = '<!DOCTYPE html>
<html>
<head>
    <title>Тест API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .result { background: #2a2a2a; padding: 15px; margin: 10px 0; border-radius: 5px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #333; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 Тест API getReferralStats</h1>
    
    <button onclick="testDebugAPI()">Тест Debug API</button>
    <button onclick="testReferralStats()">Тест Referral Stats</button>
    <button onclick="clearResults()">Очистить</button>
    
    <div id="results"></div>
    
    <script>
        function addResult(title, data) {
            const results = document.getElementById("results");
            const div = document.createElement("div");
            div.className = "result";
            div.innerHTML = `<h3>${title}</h3><pre>${JSON.stringify(data, null, 2)}</pre>`;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById("results").innerHTML = "";
        }
        
        async function testDebugAPI() {
            try {
                const response = await fetch("/api/debug_api.php");
                const data = await response.json();
                addResult("🔍 Debug API", data);
            } catch (error) {
                addResult("❌ Debug API Error", { error: error.message });
            }
        }
        
        async function testReferralStats() {
            try {
                // Тестируем с пользователем который должен иметь заработок
                const testData = {
                    initData: "user=" + encodeURIComponent(JSON.stringify({
                        id: 5880288830,
                        username: "alter_mega_ego",
                        first_name: "Альтер",
                        last_name: "Эго"
                    })) + "&auth_date=" + Math.floor(Date.now() / 1000) + "&hash=test_hash"
                };
                
                const response = await fetch("/api/getReferralStats.php", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                addResult("📊 Referral Stats API", data);
            } catch (error) {
                addResult("❌ Referral Stats Error", { error: error.message });
            }
        }
    </script>
</body>
</html>';

file_put_contents('test_api.html', $testPageContent);
echo "  ✅ Создан файл test_api.html для тестирования в браузере\n";

echo "\n=== ИНСТРУКЦИИ ===\n";
echo "1. Откройте в браузере: http://argun-defolt.loc/test_api.html\n";
echo "2. Нажмите кнопки для тестирования API\n";
echo "3. Проверьте что возвращает API\n";
echo "4. Сравните с данными выше\n";

echo "\n📊 ОЖИДАЕМЫЕ РЕЗУЛЬТАТЫ:\n";
echo "• Debug API должен показать пользователей с заработком\n";
echo "• Referral Stats должен вернуть totalEarned > 0 для @alter_mega_ego\n";
echo "• Если API работает, проблема в интерфейсе\n";
echo "• Если API не работает, проблема в серверной части\n";
?>
