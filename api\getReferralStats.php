<?php
/**
 * api/getReferralStats.php
 * API эндпоинт для получения статистики рефералов пользователя.
 */

// Включаем логирование для этого скрипта
ini_set('display_errors', 1); error_reporting(E_ALL);

header('Content-Type: application/json'); // Устанавливаем правильный заголовок

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) { http_response_code(500); error_log('FATAL: config.php not found in getReferralStats.php'); echo json_encode(['error'=>'Ошибка сервера: CFG']); exit; }
if (!(@require_once __DIR__ . '/validate_initdata.php')) { http_response_code(500); error_log('FATAL: validate_initdata.php not found in getReferralStats.php'); echo json_encode(['error'=>'Ошибка сервера: VID']); exit; }
if (!(@require_once __DIR__ . '/db_mock.php')) { http_response_code(500); error_log('FATAL: db_mock.php not found in getReferralStats.php'); echo json_encode(['error'=>'Ошибка сервера: DBM']); exit; }

// --- Конец проверки зависимостей ---

// 1. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'Ошибка запроса: Некорректные или отсутствующие данные']);
    exit;
}
$initData = $input['initData'];
error_log("getReferralStats INFO: Получен initData (длина: " . strlen($initData) . ")"); // Логируем получение данных

// 2. Валидация initData (С FALLBACK МЕХАНИЗМОМ)
$validatedData = validateTelegramInitData($initData);

// Проверяем результат валидации
if ($validatedData === false) {
    // ВРЕМЕННОЕ РЕШЕНИЕ: Пытаемся извлечь данные пользователя из initData напрямую
    error_log("getReferralStats WARNING: Стандартная валидация не прошла, пробуем упрощенную");

    $initDataParts = [];
    parse_str($initData, $initDataParts);

    if (isset($initDataParts['user'])) {
        $userArray = json_decode($initDataParts['user'], true);
        if ($userArray !== null && isset($userArray['id'])) {
            $validatedData = ['user' => $userArray];
            error_log("getReferralStats INFO: Упрощенная валидация прошла для пользователя " . $userArray['id']);
        } else {
            error_log("getReferralStats ERROR: Не удалось извлечь данные пользователя из initData");
            http_response_code(403);
            echo json_encode(['error' => 'Ошибка: Неверные данные пользователя']);
            exit;
        }
    } else {
        error_log("getReferralStats ERROR: Отсутствуют данные пользователя в initData");
        http_response_code(403);
        echo json_encode(['error' => 'Ошибка: Отсутствуют данные пользователя']);
        exit;
    }
}
error_log("getReferralStats INFO: initData успешно валидирован для пользователя " . ($validatedData['user']['id'] ?? '???'));

// 3. Получение ID пользователя (теперь мы уверены, что $validatedData - массив с user[id])
$userId = intval($validatedData['user']['id']);

// 4. Загрузка данных из "базы"
$userData = loadUserData();
if (!is_array($userData)) {
     error_log("getReferralStats ERROR: loadUserData вернул не массив. Проблема с файлом данных?");
     http_response_code(500);
     echo json_encode(['error' => 'Ошибка сервера: LD1']);
     exit;
}
error_log("getReferralStats INFO: Данные пользователей загружены для user $userId.");

// 5. Проверка существования пользователя
if (!isset($userData[$userId])) {
    error_log("getReferralStats WARNING: Пользователь $userId не найден в базе данных");
    http_response_code(200); // OK, но пустые данные
    echo json_encode([
        'referralsCount' => 0,
        'referrals' => [],
        'referrer' => null
    ]);
    exit;
}

// 6. Получение статистики рефералов
$referralsCount = isset($userData[$userId]['referrals_count']) ? $userData[$userId]['referrals_count'] : 0;
$referrals = isset($userData[$userId]['referrals']) ? $userData[$userId]['referrals'] : [];
$referrer = isset($userData[$userId]['referrer_id']) ? $userData[$userId]['referrer_id'] : null;

// 7. Подготовка детальной информации о рефералах
$referralsDetails = [];
foreach ($referrals as $referralId) {
    // Проверяем что $referralId является числом или строкой с числом
    if (!is_numeric($referralId)) {
        error_log("getReferralStats WARNING: Некорректный ID реферала: " . var_export($referralId, true));
        continue;
    }

    $referralId = intval($referralId); // Приводим к числу

    if (isset($userData[$referralId])) {
        // Формируем отображаемое имя пользователя
        $displayName = '';

        // Если есть username, используем его
        if (!empty($userData[$referralId]['username'])) {
            $displayName = '@' . $userData[$referralId]['username'];
        }
        // Если есть имя и/или фамилия, используем их
        elseif (!empty($userData[$referralId]['first_name']) || !empty($userData[$referralId]['last_name'])) {
            $fullName = '';
            if (!empty($userData[$referralId]['first_name'])) {
                $fullName .= $userData[$referralId]['first_name'];
            }
            if (!empty($userData[$referralId]['last_name'])) {
                $fullName .= ' ' . $userData[$referralId]['last_name'];
            }
            $displayName = trim($fullName);
        }
        // Если нет ни username, ни имени, используем ID
        if (empty($displayName)) {
            $displayName = 'Пользователь #' . $referralId;
        }

        $referralsDetails[] = [
            'id' => $referralId,
            'balance' => $userData[$referralId]['balance'] ?? 0,
            'joined' => $userData[$referralId]['joined'] ?? time(),
            'username' => $userData[$referralId]['username'] ?? null,
            'first_name' => $userData[$referralId]['first_name'] ?? null,
            'last_name' => $userData[$referralId]['last_name'] ?? null,
            'display_name' => $displayName
        ];
    }
}

// 8. Подготовка информации о реферере
$referrerInfo = null;
if ($referrer !== null && isset($userData[$referrer])) {
    // Формируем отображаемое имя реферера
    $displayName = '';

    // Если есть username, используем его
    if (!empty($userData[$referrer]['username'])) {
        $displayName = '@' . $userData[$referrer]['username'];
    }
    // Если есть имя и/или фамилия, используем их
    elseif (!empty($userData[$referrer]['first_name']) || !empty($userData[$referrer]['last_name'])) {
        $fullName = '';
        if (!empty($userData[$referrer]['first_name'])) {
            $fullName .= $userData[$referrer]['first_name'];
        }
        if (!empty($userData[$referrer]['last_name'])) {
            $fullName .= ' ' . $userData[$referrer]['last_name'];
        }
        $displayName = trim($fullName);
    }
    // Если нет ни username, ни имени, используем ID
    if (empty($displayName)) {
        $displayName = 'Пользователь #' . $referrer;
    }

    $referrerInfo = [
        'id' => $referrer,
        'username' => $userData[$referrer]['username'] ?? null,
        'first_name' => $userData[$referrer]['first_name'] ?? null,
        'last_name' => $userData[$referrer]['last_name'] ?? null,
        'display_name' => $displayName
    ];
}

// 9. Успешный ответ
http_response_code(200); // OK
echo json_encode([
    'referralsCount' => $referralsCount,
    'referrals' => $referralsDetails,
    'referrer' => $referrer,
    'referrerInfo' => $referrerInfo
]);
error_log("getReferralStats INFO: Успешный ответ отправлен для user $userId. Рефералов: $referralsCount");
exit;
?>
