<?php
/**
 * api/admin/users.php
 * Страница управления пользователями
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    // Если пользователь не аутентифицирован, перенаправляем на страницу входа
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) {
    http_response_code(500);
    error_log('FATAL: config.php not found in admin/users.php');
    die('Ошибка: Не удалось загрузить config.php');
}
if (!(@require_once __DIR__ . '/../db_mock.php')) {
    http_response_code(500);
    error_log('FATAL: db_mock.php not found in admin/users.php');
    die('Ошибка: Не удалось загрузить db_mock.php');
}
if (!(@require_once __DIR__ . '/../security.php')) {
    http_response_code(500);
    error_log('FATAL: security.php not found in admin/users.php');
    die('Ошибка: Не удалось загрузить security.php');
}
// --- Конец проверки зависимостей ---

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Обработка действий
$actionMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && isset($_POST['user_id'])) {
    $userId = intval($_POST['user_id']);

    switch ($_POST['action']) {
        case 'update_balance':
            if (isset($_POST['new_balance']) && is_numeric($_POST['new_balance'])) {
                $newBalance = intval($_POST['new_balance']);

                if (isset($userData[$userId])) {
                    $oldBalance = $userData[$userId]['balance'];
                    $userData[$userId]['balance'] = $newBalance;

                    // Обновляем total_earned, если новый баланс больше старого
                    if ($newBalance > $oldBalance) {
                        $difference = $newBalance - $oldBalance;
                        if (!isset($userData[$userId]['total_earned'])) {
                            $userData[$userId]['total_earned'] = $oldBalance + $difference;
                        } else {
                            $userData[$userId]['total_earned'] += $difference;
                        }
                    }

                    if (saveUserData($userData)) {
                        $actionMessage = "Баланс пользователя $userId успешно обновлен с $oldBalance на $newBalance";

                        // Логируем изменение баланса
                        logAuditEvent('admin_update_balance', $userId, [
                            'admin_username' => $_SESSION['admin_username'],
                            'old_balance' => $oldBalance,
                            'new_balance' => $newBalance
                        ]);
                    } else {
                        $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                    }
                } else {
                    $actionMessage = "Ошибка: Пользователь $userId не найден";
                }
            } else {
                $actionMessage = "Ошибка: Некорректное значение баланса";
            }
            break;

        case 'block_user':
            if (isset($userData[$userId])) {
                $userData[$userId]['blocked'] = true;
                $userData[$userId]['blocked_at'] = time();

                if (saveUserData($userData)) {
                    $actionMessage = "Пользователь $userId успешно заблокирован";

                    // Логируем блокировку пользователя
                    logAuditEvent('admin_block_user', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;

        case 'unblock_user':
            if (isset($userData[$userId])) {
                $userData[$userId]['blocked'] = false;

                if (saveUserData($userData)) {
                    $actionMessage = "Пользователь $userId успешно разблокирован";

                    // Логируем разблокировку пользователя
                    logAuditEvent('admin_unblock_user', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;

        case 'reset_suspicious':
            if (isset($userData[$userId])) {
                $userData[$userId]['suspicious_activity'] = 0;

                if (saveUserData($userData)) {
                    $actionMessage = "Счетчик подозрительной активности пользователя $userId успешно сброшен";

                    // Логируем сброс счетчика подозрительной активности
                    logAuditEvent('admin_reset_suspicious', $userId, [
                        'admin_username' => $_SESSION['admin_username']
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;

        case 'delete_user':
            if (isset($userData[$userId])) {
                // Сохраняем информацию о пользователе для логирования
                $deletedUserInfo = [
                    'id' => $userId,
                    'username' => $userData[$userId]['username'] ?? null,
                    'first_name' => $userData[$userId]['first_name'] ?? null,
                    'last_name' => $userData[$userId]['last_name'] ?? null,
                    'balance' => $userData[$userId]['balance'] ?? 0,
                    'referrals_count' => $userData[$userId]['referrals_count'] ?? 0
                ];

                // Удаляем пользователя из списка рефералов его реферера
                if (!empty($userData[$userId]['referrer_id'])) {
                    $referrerId = $userData[$userId]['referrer_id'];
                    if (isset($userData[$referrerId]) && isset($userData[$referrerId]['referrals'])) {
                        $userData[$referrerId]['referrals'] = array_filter(
                            $userData[$referrerId]['referrals'],
                            function($id) use ($userId) { return $id != $userId; }
                        );
                        $userData[$referrerId]['referrals_count'] = count($userData[$referrerId]['referrals']);
                    }
                }

                // Удаляем реферальные связи для рефералов этого пользователя
                if (!empty($userData[$userId]['referrals'])) {
                    foreach ($userData[$userId]['referrals'] as $referralId) {
                        if (isset($userData[$referralId])) {
                            $userData[$referralId]['referrer_id'] = null;
                        }
                    }
                }

                // Удаляем пользователя
                unset($userData[$userId]);

                if (saveUserData($userData)) {
                    $actionMessage = "Пользователь $userId успешно удален";

                    // Логируем удаление пользователя
                    logAuditEvent('admin_delete_user', $userId, [
                        'admin_username' => $_SESSION['admin_username'],
                        'deleted_user_info' => $deletedUserInfo
                    ]);
                } else {
                    $actionMessage = "Ошибка: Не удалось сохранить данные после удаления пользователя $userId";
                }
            } else {
                $actionMessage = "Ошибка: Пользователь $userId не найден";
            }
            break;
    }
}

// Обработка добавления нового пользователя
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_user') {
    $newUserId = isset($_POST['new_user_id']) ? intval($_POST['new_user_id']) : 0;
    $newUsername = isset($_POST['new_username']) ? trim($_POST['new_username']) : '';
    $newFirstName = isset($_POST['new_first_name']) ? trim($_POST['new_first_name']) : '';
    $newLastName = isset($_POST['new_last_name']) ? trim($_POST['new_last_name']) : '';
    $newBalance = isset($_POST['new_balance']) ? intval($_POST['new_balance']) : 0;

    // Валидация данных
    if ($newUserId <= 0) {
        $actionMessage = "Ошибка: ID пользователя должен быть положительным числом";
    } elseif (isset($userData[$newUserId])) {
        $actionMessage = "Ошибка: Пользователь с ID $newUserId уже существует";
    } elseif (empty($newUsername) && empty($newFirstName) && empty($newLastName)) {
        $actionMessage = "Ошибка: Необходимо указать хотя бы имя пользователя или имя/фамилию";
    } else {
        // Создаем нового пользователя
        $newUser = [
            'id' => $newUserId,
            'balance' => max(0, $newBalance),
            'total_earned' => max(0, $newBalance),
            'joined' => time(),
            'referrer_id' => null,
            'referrals' => [],
            'referrals_count' => 0,
            'referral_earnings' => 0,
            'suspicious_activity' => 0,
            'blocked' => false
        ];

        // Добавляем опциональные поля
        if (!empty($newUsername)) {
            $newUser['username'] = $newUsername;
        }
        if (!empty($newFirstName)) {
            $newUser['first_name'] = $newFirstName;
        }
        if (!empty($newLastName)) {
            $newUser['last_name'] = $newLastName;
        }

        // Добавляем пользователя в базу данных
        $userData[$newUserId] = $newUser;

        if (saveUserData($userData)) {
            $actionMessage = "Пользователь $newUserId успешно добавлен";

            // Логируем добавление пользователя
            logAuditEvent('admin_add_user', $newUserId, [
                'admin_username' => $_SESSION['admin_username'],
                'new_user_info' => [
                    'id' => $newUserId,
                    'username' => $newUsername,
                    'first_name' => $newFirstName,
                    'last_name' => $newLastName,
                    'balance' => $newBalance
                ]
            ]);
        } else {
            $actionMessage = "Ошибка: Не удалось сохранить данные нового пользователя";
        }
    }
}

// Сортировка пользователей
$sortBy = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$sortOrder = isset($_GET['order']) && $_GET['order'] === 'desc' ? 'desc' : 'asc';

// Функция для сортировки пользователей
function sortUsers($userData, $sortBy, $sortOrder) {
    $sortedUsers = $userData;

    uasort($sortedUsers, function($a, $b) use ($sortBy, $sortOrder) {
        switch ($sortBy) {
            case 'balance':
                $aValue = $a['balance'] ?? 0;
                $bValue = $b['balance'] ?? 0;
                break;
            case 'referrals':
                $aValue = $a['referrals_count'] ?? 0;
                $bValue = $b['referrals_count'] ?? 0;
                break;
            case 'joined':
                $aValue = $a['joined'] ?? 0;
                $bValue = $b['joined'] ?? 0;
                break;
            case 'suspicious':
                $aValue = $a['suspicious_activity'] ?? 0;
                $bValue = $b['suspicious_activity'] ?? 0;
                break;
            case 'username':
                // Сортировка по имени пользователя
                // Сначала проверяем username
                $aValue = $a['username'] ?? '';
                $bValue = $b['username'] ?? '';

                // Если username отсутствует, используем имя и фамилию
                if (empty($aValue)) {
                    $aFirstName = $a['first_name'] ?? '';
                    $aLastName = $a['last_name'] ?? '';
                    $aValue = $aFirstName . ' ' . $aLastName;
                }

                if (empty($bValue)) {
                    $bFirstName = $b['first_name'] ?? '';
                    $bLastName = $b['last_name'] ?? '';
                    $bValue = $bFirstName . ' ' . $bLastName;
                }

                // Приводим к нижнему регистру для корректного сравнения
                $aValue = mb_strtolower(trim($aValue));
                $bValue = mb_strtolower(trim($bValue));
                break;
            default:
                return 0;
        }

        if ($aValue == $bValue) {
            return 0;
        }

        if ($sortOrder === 'asc') {
            return ($aValue < $bValue) ? -1 : 1;
        } else {
            return ($aValue > $bValue) ? -1 : 1;
        }
    });

    return $sortedUsers;
}

// Сортируем пользователей
$sortedUsers = sortUsers($userData, $sortBy, $sortOrder);

// Пагинация
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$perPage = 20;
$totalUsers = count($sortedUsers);
$totalPages = ceil($totalUsers / $perPage);
$page = max(1, min($page, $totalPages));
$offset = ($page - 1) * $perPage;

// Получаем пользователей для текущей страницы
$usersPage = array_slice($sortedUsers, $offset, $perPage, true);

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Боковое меню -->
        <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-speedometer2 me-2"></i>
                            Панель управления
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="users.php">
                            <i class="bi bi-people me-2"></i>
                            Пользователи
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="stats.php">
                            <i class="bi bi-bar-chart me-2"></i>
                            Статистика
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="withdrawals.php">
                            <i class="bi bi-cash-stack me-2"></i>
                            Отчёты по выводам
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="security.php">
                            <i class="bi bi-shield-lock me-2"></i>
                            Безопасность
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear me-2"></i>
                            Настройки
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            Выход
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Управление пользователями</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="bi bi-person-plus"></i> Добавить пользователя
                        </button>
                        <a href="users.php" class="btn btn-sm btn-outline-secondary">Обновить</a>
                    </div>
                </div>
            </div>

            <?php if (!empty($actionMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $actionMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Таблица пользователей -->
            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>
                                <a href="users.php?sort=username&order=<?php echo $sortBy === 'username' && $sortOrder === 'asc' ? 'desc' : 'asc'; ?>">
                                    Пользователь
                                    <?php if ($sortBy === 'username'): ?>
                                        <i class="bi bi-arrow-<?php echo $sortOrder === 'asc' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="users.php?sort=balance&order=<?php echo $sortBy === 'balance' && $sortOrder === 'asc' ? 'desc' : 'asc'; ?>">
                                    Баланс
                                    <?php if ($sortBy === 'balance'): ?>
                                        <i class="bi bi-arrow-<?php echo $sortOrder === 'asc' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="users.php?sort=referrals&order=<?php echo $sortBy === 'referrals' && $sortOrder === 'asc' ? 'desc' : 'asc'; ?>">
                                    Рефералы
                                    <?php if ($sortBy === 'referrals'): ?>
                                        <i class="bi bi-arrow-<?php echo $sortOrder === 'asc' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="users.php?sort=joined&order=<?php echo $sortBy === 'joined' && $sortOrder === 'asc' ? 'desc' : 'asc'; ?>">
                                    Дата регистрации
                                    <?php if ($sortBy === 'joined'): ?>
                                        <i class="bi bi-arrow-<?php echo $sortOrder === 'asc' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="users.php?sort=suspicious&order=<?php echo $sortBy === 'suspicious' && $sortOrder === 'asc' ? 'desc' : 'asc'; ?>">
                                    Подозрительность
                                    <?php if ($sortBy === 'suspicious'): ?>
                                        <i class="bi bi-arrow-<?php echo $sortOrder === 'asc' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>Статус</th>
                            <th>Действия</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($usersPage as $userId => $user): ?>
                            <tr>
                                <td><?php echo $userId; ?></td>
                                <td>
                                    <?php
                                    // Формируем отображаемое имя пользователя
                                    $displayName = '';

                                    // Если есть username, показываем его
                                    if (!empty($user['username'])) {
                                        $displayName .= '@' . $user['username'];
                                    }

                                    // Если есть имя и/или фамилия, показываем их
                                    $fullName = '';
                                    if (!empty($user['first_name'])) {
                                        $fullName .= $user['first_name'];
                                    }
                                    if (!empty($user['last_name'])) {
                                        $fullName .= ' ' . $user['last_name'];
                                    }

                                    // Если есть и username, и имя, показываем оба
                                    if (!empty($displayName) && !empty($fullName)) {
                                        $displayName .= ' (' . $fullName . ')';
                                    }
                                    // Если есть только имя, показываем его
                                    elseif (empty($displayName) && !empty($fullName)) {
                                        $displayName = $fullName;
                                    }

                                    // Если нет ни username, ни имени, показываем ID
                                    if (empty($displayName)) {
                                        $displayName = 'Пользователь #' . $userId;
                                    }

                                    echo htmlspecialchars($displayName);
                                    ?>
                                </td>
                                <td><?php echo $user['balance'] ?? 0; ?></td>
                                <td><?php echo $user['referrals_count'] ?? 0; ?></td>
                                <td><?php echo isset($user['joined']) ? date('Y-m-d H:i', $user['joined']) : 'Неизвестно'; ?></td>
                                <td>
                                    <?php if (isset($user['suspicious_activity']) && $user['suspicious_activity'] > 0): ?>
                                        <span class="badge bg-warning"><?php echo $user['suspicious_activity']; ?></span>
                                    <?php else: ?>
                                        <span class="badge bg-success">0</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (isset($user['blocked']) && $user['blocked']): ?>
                                        <span class="badge bg-danger">Заблокирован</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">Активен</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editUserModal<?php echo $userId; ?>">
                                        <i class="bi bi-pencil"></i>
                                    </button>

                                    <?php if (isset($user['blocked']) && $user['blocked']): ?>
                                        <form method="post" class="d-inline">
                                            <input type="hidden" name="action" value="unblock_user">
                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                            <button type="submit" class="btn btn-sm btn-success">
                                                <i class="bi bi-unlock"></i>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <form method="post" class="d-inline">
                                            <input type="hidden" name="action" value="block_user">
                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger">
                                                <i class="bi bi-lock"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>

                                    <?php if (isset($user['suspicious_activity']) && $user['suspicious_activity'] > 0): ?>
                                        <form method="post" class="d-inline">
                                            <input type="hidden" name="action" value="reset_suspicious">
                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                            <button type="submit" class="btn btn-sm btn-warning">
                                                <i class="bi bi-arrow-counterclockwise"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>

                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteUserModal<?php echo $userId; ?>">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>

                            <!-- Модальное окно редактирования пользователя -->
                            <div class="modal fade" id="editUserModal<?php echo $userId; ?>" tabindex="-1" aria-labelledby="editUserModalLabel<?php echo $userId; ?>" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <?php
                                            // Формируем заголовок с именем пользователя
                                            $modalTitle = 'Редактирование пользователя #' . $userId;
                                            if (!empty($user['username']) || !empty($user['first_name']) || !empty($user['last_name'])) {
                                                $modalTitle .= ' - ';
                                                if (!empty($user['username'])) {
                                                    $modalTitle .= '@' . $user['username'];
                                                } else {
                                                    $name = '';
                                                    if (!empty($user['first_name'])) {
                                                        $name .= $user['first_name'];
                                                    }
                                                    if (!empty($user['last_name'])) {
                                                        $name .= ' ' . $user['last_name'];
                                                    }
                                                    $modalTitle .= $name;
                                                }
                                            }
                                            ?>
                                            <h5 class="modal-title" id="editUserModalLabel<?php echo $userId; ?>"><?php echo htmlspecialchars($modalTitle); ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <form method="post">
                                                <input type="hidden" name="action" value="update_balance">
                                                <input type="hidden" name="user_id" value="<?php echo $userId; ?>">

                                                <!-- Информация о пользователе -->
                                                <?php if (!empty($user['username']) || !empty($user['first_name']) || !empty($user['last_name'])): ?>
                                                <div class="card mb-3">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Информация о пользователе</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <?php if (!empty($user['username'])): ?>
                                                        <div class="mb-2">
                                                            <strong>Имя пользователя:</strong> @<?php echo htmlspecialchars($user['username']); ?>
                                                        </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($user['first_name']) || !empty($user['last_name'])): ?>
                                                        <div class="mb-2">
                                                            <strong>Имя:</strong>
                                                            <?php
                                                            $fullName = '';
                                                            if (!empty($user['first_name'])) {
                                                                $fullName .= $user['first_name'];
                                                            }
                                                            if (!empty($user['last_name'])) {
                                                                $fullName .= ' ' . $user['last_name'];
                                                            }
                                                            echo htmlspecialchars($fullName);
                                                            ?>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <?php endif; ?>

                                                <div class="mb-3">
                                                    <label for="new_balance<?php echo $userId; ?>" class="form-label">Баланс</label>
                                                    <input type="number" class="form-control" id="new_balance<?php echo $userId; ?>" name="new_balance" value="<?php echo $user['balance'] ?? 0; ?>" min="0">
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">Дата регистрации</label>
                                                    <input type="text" class="form-control" value="<?php echo isset($user['joined']) ? date('Y-m-d H:i', $user['joined']) : 'Неизвестно'; ?>" disabled>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">Рефералы</label>
                                                    <input type="text" class="form-control" value="<?php echo $user['referrals_count'] ?? 0; ?>" disabled>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">Заработано на рефералах</label>
                                                    <input type="text" class="form-control" value="<?php echo $user['referral_earnings'] ?? 0; ?>" disabled>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">Подозрительная активность</label>
                                                    <input type="text" class="form-control" value="<?php echo $user['suspicious_activity'] ?? 0; ?>" disabled>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">Статус</label>
                                                    <input type="text" class="form-control" value="<?php echo isset($user['blocked']) && $user['blocked'] ? 'Заблокирован' : 'Активен'; ?>" disabled>
                                                </div>

                                                <button type="submit" class="btn btn-primary">Сохранить</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Модальное окно удаления пользователя -->
                            <div class="modal fade" id="deleteUserModal<?php echo $userId; ?>" tabindex="-1" aria-labelledby="deleteUserModalLabel<?php echo $userId; ?>" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="deleteUserModalLabel<?php echo $userId; ?>">Удаление пользователя</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="alert alert-danger">
                                                <i class="bi bi-exclamation-triangle"></i>
                                                <strong>Внимание!</strong> Это действие нельзя отменить.
                                            </div>

                                            <p>Вы действительно хотите удалить пользователя?</p>

                                            <div class="card">
                                                <div class="card-body">
                                                    <h6 class="card-title">Информация о пользователе:</h6>
                                                    <p class="card-text">
                                                        <strong>ID:</strong> <?php echo $userId; ?><br>
                                                        <?php if (!empty($user['username'])): ?>
                                                        <strong>Username:</strong> @<?php echo htmlspecialchars($user['username']); ?><br>
                                                        <?php endif; ?>
                                                        <?php if (!empty($user['first_name']) || !empty($user['last_name'])): ?>
                                                        <strong>Имя:</strong>
                                                        <?php
                                                        $fullName = '';
                                                        if (!empty($user['first_name'])) {
                                                            $fullName .= $user['first_name'];
                                                        }
                                                        if (!empty($user['last_name'])) {
                                                            $fullName .= ' ' . $user['last_name'];
                                                        }
                                                        echo htmlspecialchars($fullName);
                                                        ?><br>
                                                        <?php endif; ?>
                                                        <strong>Баланс:</strong> <?php echo $user['balance'] ?? 0; ?> монет<br>
                                                        <strong>Рефералов:</strong> <?php echo $user['referrals_count'] ?? 0; ?>
                                                    </p>
                                                </div>
                                            </div>

                                            <p class="mt-3 text-muted">
                                                <small>
                                                    При удалении пользователя:
                                                    <br>• Пользователь будет удален из списка рефералов своего реферера
                                                    <br>• У всех рефералов этого пользователя будет удалена связь с реферером
                                                    <br>• Все данные пользователя будут безвозвратно потеряны
                                                </small>
                                            </p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                                            <form method="post" class="d-inline">
                                                <input type="hidden" name="action" value="delete_user">
                                                <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                                <button type="submit" class="btn btn-danger">
                                                    <i class="bi bi-trash"></i> Удалить пользователя
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Пагинация -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Навигация по страницам">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="users.php?page=<?php echo $page - 1; ?>&sort=<?php echo $sortBy; ?>&order=<?php echo $sortOrder; ?>" aria-label="Предыдущая">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="users.php?page=<?php echo $i; ?>&sort=<?php echo $sortBy; ?>&order=<?php echo $sortOrder; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="users.php?page=<?php echo $page + 1; ?>&sort=<?php echo $sortBy; ?>&order=<?php echo $sortOrder; ?>" aria-label="Следующая">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        </main>
    </div>
</div>

<!-- Модальное окно добавления пользователя -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Добавить нового пользователя</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_user">

                    <div class="mb-3">
                        <label for="new_user_id" class="form-label">ID пользователя <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="new_user_id" name="new_user_id" required min="1" placeholder="Например: 123456789">
                        <div class="form-text">Уникальный числовой ID пользователя Telegram</div>
                    </div>

                    <div class="mb-3">
                        <label for="new_username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text">@</span>
                            <input type="text" class="form-control" id="new_username" name="new_username" placeholder="username">
                        </div>
                        <div class="form-text">Username пользователя в Telegram (без @)</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_first_name" class="form-label">Имя</label>
                                <input type="text" class="form-control" id="new_first_name" name="new_first_name" placeholder="Имя">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_last_name" class="form-label">Фамилия</label>
                                <input type="text" class="form-control" id="new_last_name" name="new_last_name" placeholder="Фамилия">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="new_balance" class="form-label">Начальный баланс</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="new_balance" name="new_balance" value="0" min="0" placeholder="0">
                            <span class="input-group-text">монет</span>
                        </div>
                        <div class="form-text">Начальный баланс пользователя (по умолчанию 0)</div>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Примечание:</strong> Необходимо указать хотя бы username или имя/фамилию пользователя.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-person-plus"></i> Добавить пользователя
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
