<?php
/**
 * Тест с правильной минимальной суммой
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🎯 ТЕСТ С ПРАВИЛЬНОЙ МИНИМАЛЬНОЙ СУММОЙ\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

echo "📊 Проверяем баланс:\n";
$balance = $api->getAccountBalance();
if ($balance) {
    foreach ($balance as $currency => $data) {
        if (is_array($data) && isset($data['amount']) && $data['amount'] > 0) {
            echo "- {$currency}: {$data['amount']}\n";
        }
    }
} else {
    echo "❌ Не удалось получить баланс\n";
    exit;
}

echo "\n💡 Из ошибки мы знаем:\n";
echo "- USDT TRC20 минимум: 8.********\n";
echo "- У нас на балансе: 1.058 USDT\n";
echo "- Проблема: недостаточно средств!\n\n";

echo "🔧 РЕШЕНИЯ:\n\n";

echo "1️⃣ Попробуем с TRX (у нас 1.149006 TRX):\n";

$testAddress = 'TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK'; // TRX адрес
$testCurrency = 'trx';
$testAmount = 1.0; // 1 TRX

echo "Параметры:\n";
echo "- Адрес: {$testAddress}\n";
echo "- Валюта: {$testCurrency}\n";
echo "- Сумма: {$testAmount}\n\n";

echo "Создание выплаты TRX...\n";

$result = $api->createPayoutWithFeeHandling($testAddress, $testCurrency, $testAmount);

if ($result) {
    if (isset($result['error'])) {
        echo "❌ Ошибка TRX: {$result['message']}\n";
        if (isset($result['code'])) {
            echo "Код: {$result['code']}\n";
        }
    } else {
        echo "✅ ВЫПЛАТА TRX СОЗДАНА!\n";
        echo "🆔 ID: {$result['id']}\n";
        
        if (isset($result['withdrawals'][0])) {
            $withdrawal = $result['withdrawals'][0];
            echo "📊 Статус: {$withdrawal['status']}\n";
            echo "💰 Сумма: {$withdrawal['amount']} {$withdrawal['currency']}\n";
        }
    }
}

echo "\n" . str_repeat("-", 50) . "\n\n";

echo "2️⃣ Попробуем с BTC (у нас 0.00001 BTC):\n";

$btcAddress = '**********************************'; // BTC адрес
$btcCurrency = 'btc';
$btcAmount = 0.000005; // 5 сатоши

echo "Параметры:\n";
echo "- Адрес: {$btcAddress}\n";
echo "- Валюта: {$btcCurrency}\n";
echo "- Сумма: {$btcAmount}\n\n";

echo "Создание выплаты BTC...\n";

$btcResult = $api->createPayoutWithFeeHandling($btcAddress, $btcCurrency, $btcAmount);

if ($btcResult) {
    if (isset($btcResult['error'])) {
        echo "❌ Ошибка BTC: {$btcResult['message']}\n";
        if (isset($btcResult['code'])) {
            echo "Код: {$btcResult['code']}\n";
        }
    } else {
        echo "✅ ВЫПЛАТА BTC СОЗДАНА!\n";
        echo "🆔 ID: {$btcResult['id']}\n";
        
        if (isset($btcResult['withdrawals'][0])) {
            $withdrawal = $btcResult['withdrawals'][0];
            echo "📊 Статус: {$withdrawal['status']}\n";
            echo "💰 Сумма: {$withdrawal['amount']} {$withdrawal['currency']}\n";
        }
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎯 ВЫВОДЫ\n";
echo str_repeat("=", 60) . "\n\n";

echo "❌ ПРОБЛЕМА С USDT TRC20:\n";
echo "- Минимальная сумма: 8.58 USDT\n";
echo "- Наш баланс: 1.058 USDT\n";
echo "- Результат: недостаточно средств\n\n";

echo "💡 РЕШЕНИЯ:\n";
echo "1. Пополнить баланс USDT TRC20 до 9+ USDT\n";
echo "2. Использовать другие валюты (TRX, BTC, ETH)\n";
echo "3. Настроить автоконвертацию между валютами\n";
echo "4. Предупреждать пользователей о минимальных суммах\n\n";

echo "🔧 ДЛЯ ПРОДАКШЕНА:\n";
echo "1. Добавить проверку минимальных сумм в интерфейс\n";
echo "2. Показывать пользователям актуальные минимумы\n";
echo "3. Предлагать альтернативные валюты\n";
echo "4. Реализовать автоконвертацию\n\n";

if (($result && !isset($result['error'])) || ($btcResult && !isset($btcResult['error']))) {
    echo "✅ СИСТЕМА РАБОТАЕТ!\n";
    echo "Проблема была только в минимальных суммах.\n";
    echo "С правильными суммами выплаты создаются успешно.\n";
} else {
    echo "⚠️ Требуется дополнительная настройка\n";
    echo "Возможно, нужно пополнить баланс или изменить настройки.\n";
}
?>
